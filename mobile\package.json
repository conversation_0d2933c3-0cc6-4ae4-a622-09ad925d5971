{"name": "mobile", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/metro-runtime": "~5.0.4", "@hookform/resolvers": "^5.0.1", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-picker/picker": "^2.11.0", "@react-navigation/bottom-tabs": "^7.3.13", "@react-navigation/native": "^7.1.9", "@react-navigation/stack": "^7.3.2", "@tanstack/react-query": "^5.76.1", "expo": "~53.0.9", "expo-document-picker": "^13.1.5", "expo-image-picker": "^16.1.4", "expo-status-bar": "~2.2.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.4", "react-native": "0.79.2", "react-native-document-picker": "^9.3.1", "react-native-gesture-handler": "^2.25.0", "react-native-image-picker": "^8.2.1", "react-native-paper": "^5.14.5", "react-native-reanimated": "^3.17.5", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "^4.10.0", "react-native-vector-icons": "^10.2.0", "react-native-web": "^0.19.12", "zod": "^3.25.20"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true}