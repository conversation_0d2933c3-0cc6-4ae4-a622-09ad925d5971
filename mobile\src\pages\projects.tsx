import React from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { ProjectCard, NewProjectCard } from "../components/projects/project-card";
import { Colors } from "../lib/constants";

const projects = [
  {
    title: "Family Favorites",
    recipeCount: 12,
    lastUpdated: "2 days ago",
    status: "In Progress" as const,
    contributors: [
      { initials: "J<PERSON>", color: "#6b7280" },
      { initials: "TS", color: "#3b82f6" },
      { initials: "AM", color: "#8b5cf6" },
    ],
    imageIndex: 0,
    bgColor: "#3b82f6",
  },
  {
    title: "Holiday Collection",
    recipeCount: 8,
    lastUpdated: "1 week ago",
    status: "Draft" as const,
    contributors: [
      { initials: "JD", color: "#6b7280" },
      { initials: "GR", color: "#8b5cf6" },
    ],
    imageIndex: 1,
    bgColor: "#6b7280",
  },
];

export default function Projects() {
  return (
    <ScrollView style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>Projects</Text>
        <Text style={styles.subtitle}>Create and manage your cookbook projects</Text>

        <View style={styles.grid}>
          {projects.map((project, index) => (
            <ProjectCard
              key={index}
              title={project.title}
              recipeCount={project.recipeCount}
              lastUpdated={project.lastUpdated}
              status={project.status}
              contributors={project.contributors}
              imageIndex={project.imageIndex}
              bgColor={project.bgColor}
            />
          ))}

          <NewProjectCard />
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  content: {
    padding: 16,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 8,
    fontFamily: 'serif',
    color: Colors.textPrimary,
  },
  subtitle: {
    fontSize: 18,
    color: Colors.textSecondary,
    marginBottom: 32,
  },
  grid: {
    flexDirection: 'column',
    gap: 24,
  },
});
