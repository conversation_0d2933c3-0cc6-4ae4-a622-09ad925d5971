import React from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { InviteForm } from "../components/collaborate/invite-form";
import { ContributorsList } from "../components/collaborate/contributors-list";
import { ProgressTracker } from "../components/collaborate/progress-tracker";
import { Colors } from "../lib/constants";

export default function Collaborate() {
  return (
    <ScrollView style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>Collaborate</Text>
        <Text style={styles.subtitle}>Invite family members to contribute their recipes</Text>

        <View style={styles.grid}>
          <View style={styles.leftColumn}>
            <InviteForm />
          </View>

          <View style={styles.rightColumn}>
            <ContributorsList />
            <ProgressTracker />
          </View>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  content: {
    padding: 16,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 8,
    fontFamily: 'serif',
    color: Colors.textPrimary,
  },
  subtitle: {
    fontSize: 18,
    color: Colors.textSecondary,
    marginBottom: 32,
  },
  grid: {
    flexDirection: 'column',
    gap: 32,
  },
  leftColumn: {
    flex: 3,
  },
  rightColumn: {
    flex: 2,
    gap: 16,
  },
});
