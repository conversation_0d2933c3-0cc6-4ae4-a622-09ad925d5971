import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, TextInput, Alert, ActivityIndicator } from 'react-native';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Button } from '../ui/button';
import { Card } from '../ui/card';
import { useToast } from '../../hooks/use-toast';
import { useAuth } from '../../hooks/use-auth';
import { Colors, Spacing, BorderRadius, API_URL } from '../../lib/constants';
import Icon from 'react-native-vector-icons/Feather';

interface Comment {
  id: number;
  comment: string;
  createdAt: string;
  updatedAt: string;
  recipeId: number;
  user: {
    id: number;
    name: string;
    role: string;
  };
}

interface Recipe {
  id: number;
  project?: {
    id: number;
    organizerId: number;
  };
}

interface RecipeCommentsProps {
  recipeId: number;
  hideTitle?: boolean;
}

export function RecipeComments({ recipeId, hideTitle = false }: RecipeCommentsProps) {
  const [newComment, setNewComment] = useState('');
  const [editingCommentId, setEditingCommentId] = useState<number | null>(null);
  const [editingText, setEditingText] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [commentsPerPage] = useState(5);
  const { toast } = useToast();
  const { user } = useAuth();
  const queryClient = useQueryClient();

  // Fetch recipe data to check permissions
  const { data: recipeData } = useQuery({
    queryKey: ['recipe', recipeId],
    queryFn: async () => {
      const token = await AsyncStorage.getItem('token');
      if (!token) {
        throw new Error('Not authenticated');
      }

      const response = await fetch(`${API_URL}/recipes/${recipeId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch recipe');
      }

      return response.json();
    }
  });

  // Fetch recipe comments
  const { data: commentsData, isLoading, isError } = useQuery({
    queryKey: ['recipe-comments', recipeId],
    queryFn: async () => {
      const token = await AsyncStorage.getItem('token');
      if (!token) {
        throw new Error('Not authenticated');
      }

      const response = await fetch(`${API_URL}/comments/recipe/${recipeId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch comments');
      }

      const data = await response.json();
      console.log('Comments data:', data);
      return data;
    }
  });

  // Add comment mutation
  const addCommentMutation = useMutation({
    mutationFn: async (comment: string) => {
      const token = await AsyncStorage.getItem('token');
      if (!token) {
        throw new Error('Not authenticated');
      }

      const response = await fetch(`${API_URL}/comments/recipe/${recipeId}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ comment })
      });

      if (!response.ok) {
        throw new Error('Failed to add comment');
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['recipe-comments', recipeId] });
      setNewComment('');
      toast({
        title: 'Comment added',
        description: 'Your comment has been added successfully.'
      });
      // Move to the first page when a new comment is added
      setCurrentPage(1);
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: `Failed to add comment: ${error.message}`,
        variant: 'destructive'
      });
    }
  });

  // Update comment mutation
  const updateCommentMutation = useMutation({
    mutationFn: async ({ commentId, comment }: { commentId: number; comment: string }) => {
      const token = await AsyncStorage.getItem('token');
      if (!token) {
        throw new Error('Not authenticated');
      }

      const response = await fetch(`${API_URL}/comments/${commentId}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ comment })
      });

      if (!response.ok) {
        throw new Error('Failed to update comment');
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['recipe-comments', recipeId] });
      setEditingCommentId(null);
      setEditingText('');
      toast({
        title: 'Comment updated',
        description: 'Your comment has been updated successfully.'
      });
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: `Failed to update comment: ${error.message}`,
        variant: 'destructive'
      });
    }
  });

  // Delete comment mutation
  const deleteCommentMutation = useMutation({
    mutationFn: async (commentId: number) => {
      const token = await AsyncStorage.getItem('token');
      if (!token) {
        throw new Error('Not authenticated');
      }

      const response = await fetch(`${API_URL}/comments/${commentId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to delete comment');
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['recipe-comments', recipeId] });
      toast({
        title: 'Comment deleted',
        description: 'Your comment has been deleted successfully.'
      });
      // Adjust current page if necessary after deletion
      if (comments.length <= commentsPerPage && currentPage > 1) {
        setCurrentPage(currentPage - 1);
      }
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: `Failed to delete comment: ${error.message}`,
        variant: 'destructive'
      });
    }
  });

  const handleAddComment = () => {
    if (!newComment.trim()) return;
    addCommentMutation.mutate(newComment);
  };

  const handleUpdateComment = (commentId: number) => {
    if (!editingText.trim()) return;
    updateCommentMutation.mutate({ commentId, comment: editingText });
  };

  const handleDeleteComment = (commentId: number) => {
    Alert.alert(
      'Delete Comment',
      'Are you sure you want to delete this comment?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Delete', style: 'destructive', onPress: () => deleteCommentMutation.mutate(commentId) }
      ]
    );
  };

  const startEditing = (comment: Comment) => {
    setEditingCommentId(comment.id);
    setEditingText(comment.comment);
  };

  const cancelEditing = () => {
    setEditingCommentId(null);
    setEditingText('');
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase();
  };

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) return 'just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
    return `${Math.floor(diffInSeconds / 86400)}d ago`;
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={Colors.primary} />
        <Text style={styles.loadingText}>Loading comments...</Text>
      </View>
    );
  }

  if (isError) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Failed to load comments</Text>
      </View>
    );
  }

  const comments = commentsData?.comments || [];
  const totalComments = comments.length;
  const totalPages = Math.ceil(totalComments / commentsPerPage);
  const startIndex = (currentPage - 1) * commentsPerPage;
  const currentComments = comments.slice(startIndex, startIndex + commentsPerPage);

  return (
    <View style={styles.container}>
      {!hideTitle && (
        <Text style={styles.title}>
          Comments ({totalComments})
        </Text>
      )}

      {/* Add comment form */}
      <Card style={styles.addCommentCard}>
        <View style={styles.addCommentContainer}>
          <View style={styles.avatar}>
            <Text style={styles.avatarText}>
              {user?.name ? getInitials(user.name) : 'U'}
            </Text>
          </View>
          <View style={styles.addCommentContent}>
            <TextInput
              style={styles.commentInput}
              placeholder="Share your story or add a comment about this recipe..."
              placeholderTextColor={Colors.mutedForeground}
              value={newComment}
              onChangeText={setNewComment}
              multiline
              numberOfLines={3}
            />
            <Button
              onPress={handleAddComment}
              disabled={!newComment.trim() || addCommentMutation.isPending}
              style={styles.addCommentButton}
            >
              <View style={styles.buttonContent}>
                <Icon name="send" size={16} color={Colors.primaryForeground} />
                <Text style={styles.buttonText}>
                  {addCommentMutation.isPending ? 'Posting...' : 'Post Comment'}
                </Text>
              </View>
            </Button>
          </View>
        </View>
      </Card>

      {/* Comments list */}
      {totalComments === 0 ? (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>
            No comments yet. Be the first to share a story about this recipe!
          </Text>
        </View>
      ) : (
        <View style={styles.commentsContainer}>
          {currentComments.map((comment: Comment) => (
            <Card key={comment.id} style={styles.commentCard}>
              <View style={styles.commentContainer}>
                <View style={styles.avatar}>
                  <Text style={styles.avatarText}>
                    {getInitials(comment.user.name)}
                  </Text>
                </View>
                <View style={styles.commentContent}>
                  <View style={styles.commentHeader}>
                    <View style={styles.userInfo}>
                      <Text style={styles.userName}>{comment.user.name}</Text>
                      <View style={styles.roleBadge}>
                        <Text style={styles.roleText}>{comment.user.role}</Text>
                      </View>
                    </View>
                    <Text style={styles.timeText}>
                      {formatTimeAgo(comment.createdAt)}
                    </Text>
                  </View>

                  {editingCommentId === comment.id ? (
                    <View style={styles.editContainer}>
                      <TextInput
                        style={styles.editInput}
                        value={editingText}
                        onChangeText={setEditingText}
                        multiline
                        numberOfLines={3}
                      />
                      <View style={styles.editActions}>
                        <Button
                          variant="outline"
                          size="sm"
                          onPress={cancelEditing}
                          style={styles.editButton}
                        >
                          <Text>Cancel</Text>
                        </Button>
                        <Button
                          size="sm"
                          onPress={() => handleUpdateComment(comment.id)}
                          disabled={!editingText.trim() || updateCommentMutation.isPending}
                          style={styles.editButton}
                        >
                          <Text style={styles.buttonText}>
                            {updateCommentMutation.isPending ? 'Saving...' : 'Save'}
                          </Text>
                        </Button>
                      </View>
                    </View>
                  ) : (
                    <>
                      <Text style={styles.commentText}>{comment.comment}</Text>

                      {/* Comment actions (only for the comment owner, admins, or organizers of the book) */}
                      {(() => {
                        // Check if user can edit/delete this comment
                        const canEditComment =
                          // Comment owner can edit/delete their own comment
                          user?.id === comment.user.id ||
                          // Admin can edit/delete any comment
                          user?.role === 'admin' ||
                          // Organizer can edit/delete any comments on recipes in their books
                          (user?.role === 'organizer' && recipeData?.recipe?.project?.organizerId === user?.id);

                        return canEditComment;
                      })() && (
                        <View style={styles.commentActions}>
                          <TouchableOpacity
                            style={styles.actionButton}
                            onPress={() => startEditing(comment)}
                          >
                            <Icon name="edit" size={14} color={Colors.mutedForeground} />
                          </TouchableOpacity>
                          <TouchableOpacity
                            style={styles.actionButton}
                            onPress={() => handleDeleteComment(comment.id)}
                            disabled={deleteCommentMutation.isPending}
                          >
                            <Icon name="trash-2" size={14} color={Colors.destructive} />
                          </TouchableOpacity>
                        </View>
                      )}
                    </>
                  )}
                </View>
              </View>
            </Card>
          ))}

          {/* Pagination */}
          {totalPages > 1 && (
            <View style={styles.pagination}>
              <TouchableOpacity
                style={[styles.pageButton, currentPage === 1 && styles.pageButtonDisabled]}
                onPress={() => setCurrentPage(currentPage - 1)}
                disabled={currentPage === 1}
              >
                <Icon name="chevron-left" size={16} color={currentPage === 1 ? Colors.mutedForeground : Colors.primary} />
              </TouchableOpacity>

              <Text style={styles.pageInfo}>
                Page {currentPage} of {totalPages}
              </Text>

              <TouchableOpacity
                style={[styles.pageButton, currentPage === totalPages && styles.pageButtonDisabled]}
                onPress={() => setCurrentPage(currentPage + 1)}
                disabled={currentPage === totalPages}
              >
                <Icon name="chevron-right" size={16} color={currentPage === totalPages ? Colors.mutedForeground : Colors.primary} />
              </TouchableOpacity>
            </View>
          )}
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.foreground,
    marginBottom: Spacing.md,
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: Spacing.xl,
  },
  loadingText: {
    marginTop: Spacing.sm,
    fontSize: 14,
    color: Colors.mutedForeground,
  },
  errorContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: Spacing.xl,
  },
  errorText: {
    fontSize: 14,
    color: Colors.destructive,
    textAlign: 'center',
  },
  addCommentCard: {
    marginBottom: Spacing.lg,
  },
  addCommentContainer: {
    flexDirection: 'row',
    padding: Spacing.md,
    gap: Spacing.md,
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.muted,
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.foreground,
  },
  addCommentContent: {
    flex: 1,
    gap: Spacing.sm,
  },
  commentInput: {
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: BorderRadius.md,
    padding: Spacing.md,
    fontSize: 14,
    color: Colors.foreground,
    backgroundColor: Colors.background,
    minHeight: 80,
    textAlignVertical: 'top',
  },
  addCommentButton: {
    alignSelf: 'flex-start',
  },
  buttonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.xs,
  },
  buttonText: {
    color: Colors.primaryForeground,
    fontWeight: '500',
  },
  emptyContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: Spacing.xl,
  },
  emptyText: {
    fontSize: 14,
    color: Colors.mutedForeground,
    textAlign: 'center',
  },
  commentsContainer: {
    gap: Spacing.md,
  },
  commentCard: {
    padding: 0,
  },
  commentContainer: {
    flexDirection: 'row',
    padding: Spacing.md,
    gap: Spacing.md,
  },
  commentContent: {
    flex: 1,
  },
  commentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.xs,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.sm,
  },
  userName: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.foreground,
  },
  roleBadge: {
    backgroundColor: Colors.muted,
    paddingHorizontal: Spacing.xs,
    paddingVertical: 2,
    borderRadius: BorderRadius.sm,
  },
  roleText: {
    fontSize: 10,
    color: Colors.mutedForeground,
  },
  timeText: {
    fontSize: 12,
    color: Colors.mutedForeground,
  },
  commentText: {
    fontSize: 14,
    color: Colors.mutedForeground,
    lineHeight: 20,
  },
  editContainer: {
    gap: Spacing.sm,
  },
  editInput: {
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: BorderRadius.md,
    padding: Spacing.md,
    fontSize: 14,
    color: Colors.foreground,
    backgroundColor: Colors.background,
    minHeight: 80,
    textAlignVertical: 'top',
  },
  editActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: Spacing.sm,
  },
  editButton: {
    minWidth: 80,
  },
  commentActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: Spacing.sm,
    marginTop: Spacing.sm,
  },
  actionButton: {
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  pagination: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: Spacing.md,
    marginTop: Spacing.lg,
  },
  pageButton: {
    width: 32,
    height: 32,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: BorderRadius.md,
    backgroundColor: Colors.background,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  pageButtonDisabled: {
    opacity: 0.5,
  },
  pageInfo: {
    fontSize: 14,
    color: Colors.foreground,
  },
});
