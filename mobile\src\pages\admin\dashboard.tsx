import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  ActivityIndicator,
  Alert,
  TextInput
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card';
import { Button } from '../../components/ui/button';
import { useToast } from '../../hooks/use-toast';
import { useAuth } from '../../hooks/use-auth';
import { Colors, Spacing, BorderRadius, API_URL } from '../../lib/constants';
import { IntercomAdmin } from '../../components/admin/IntercomAdmin';

interface Recipe {
  id: number;
  title: string;
  description: string;
  status: 'pending' | 'approved' | 'rejected';
  createdAt: string;
  contributor: {
    name: string;
    email: string;
  };
  project: {
    name: string;
  };
}

export default function AdminDashboard() {
  const [recipes, setRecipes] = useState<Recipe[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [loadingRecipeId, setLoadingRecipeId] = useState<number | null>(null);
  const [rejectionDialogOpen, setRejectionDialogOpen] = useState(false);
  const [selectedRecipeId, setSelectedRecipeId] = useState<number | null>(null);
  const [rejectionMessage, setRejectionMessage] = useState("");
  const { toast } = useToast();
  const { user } = useAuth();

  useEffect(() => {
    const fetchPendingRecipes = async () => {
      try {
        const token = await AsyncStorage.getItem('token');
        const response = await fetch(`${API_URL}/admin/pending-recipes`, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        if (!response.ok) {
          throw new Error("Failed to fetch pending recipes");
        }

        const data = await response.json();
        setRecipes(data.recipes);
      } catch (error) {
        console.error("Error fetching pending recipes:", error);
        toast({
          title: "Error",
          description: "Failed to load pending recipes. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchPendingRecipes();
  }, [toast]);

  const handleStatusUpdate = async (recipeId: number, status: 'approved' | 'rejected', message?: string) => {
    try {
      setLoadingRecipeId(recipeId);
      const token = await AsyncStorage.getItem('token');
      const response = await fetch(`${API_URL}/admin/recipes/${recipeId}/status`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({ status, rejectionMessage: message }),
      });

      if (!response.ok) {
        throw new Error("Failed to update recipe status");
      }

      // Update local state
      setRecipes(recipes?.filter(recipe => recipe.id !== recipeId));

      toast({
        title: "Success",
        description: `Recipe ${status} successfully.`,
      });

      // Close rejection dialog if it was open
      if (status === 'rejected') {
        setRejectionDialogOpen(false);
        setRejectionMessage("");
      }
    } catch (error) {
      console.error("Error updating recipe status:", error);
      toast({
        title: "Error",
        description: "Failed to update recipe status. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoadingRecipeId(null);
    }
  };

  const handleRejectClick = (recipeId: number) => {
    setSelectedRecipeId(recipeId);
    setRejectionDialogOpen(true);
  };

  const handleRejectConfirm = () => {
    if (selectedRecipeId) {
      handleStatusUpdate(selectedRecipeId, 'rejected', rejectionMessage);
    }
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={Colors.primary} />
        <Text style={styles.loadingText}>Loading...</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Welcome, {user?.name}</Text>
      </View>

      <Card style={styles.card}>
        <CardHeader>
          <Text style={styles.cardTitle}>Pending Recipe Approvals</Text>
          <Text style={styles.cardDescription}>
            Review and approve or reject submitted recipes
          </Text>
        </CardHeader>
        <CardContent>
          {recipes?.length === 0 ? (
            <Text style={styles.emptyText}>No pending recipes to review.</Text>
          ) : (
            <View style={styles.recipesList}>
              {recipes?.map((recipe) => (
                <Card key={recipe.id} style={styles.recipeCard}>
                  <CardHeader>
                    <Text style={styles.recipeTitle}>{recipe.title}</Text>
                    <Text style={styles.recipeDescription}>{recipe.description}</Text>
                  </CardHeader>
                  <CardContent>
                    <View style={styles.recipeContent}>
                      <View style={styles.recipeInfo}>
                        <Text style={styles.recipeDetail}>
                          Contributor: {recipe.contributor.name} ({recipe.contributor.email})
                        </Text>
                        <Text style={styles.recipeDetail}>
                          Project: {recipe.project.name}
                        </Text>
                        <Text style={styles.recipeDetail}>
                          Submitted: {new Date(recipe.createdAt).toLocaleDateString()}
                        </Text>
                      </View>
                      <View style={styles.recipeActions}>
                        <Button
                          variant="default"
                          onPress={() => handleStatusUpdate(recipe.id, 'approved')}
                          disabled={loadingRecipeId === recipe.id}
                          style={styles.actionButton}
                        >
                          {loadingRecipeId === recipe.id ? (
                            <>
                              <ActivityIndicator size="small" color={Colors.primaryForeground} />
                              <Text style={styles.loadingButtonText}>Approving...</Text>
                            </>
                          ) : (
                            'Approve'
                          )}
                        </Button>
                        <Button
                          variant="destructive"
                          onPress={() => handleRejectClick(recipe.id)}
                          disabled={loadingRecipeId === recipe.id}
                          style={styles.actionButton}
                        >
                          {loadingRecipeId === recipe.id ? (
                            <>
                              <ActivityIndicator size="small" color={Colors.destructiveForeground} />
                              <Text style={styles.loadingButtonText}>Rejecting...</Text>
                            </>
                          ) : (
                            'Reject'
                          )}
                        </Button>
                      </View>
                    </View>
                  </CardContent>
                </Card>
              ))}
            </View>
          )}
        </CardContent>
      </Card>

      {/* Live Chat & Support Management */}
      <IntercomAdmin />

      {/* Rejection Dialog Modal */}
      {rejectionDialogOpen && (
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Reject Recipe</Text>
              <Text style={styles.modalDescription}>
                Please provide a reason for rejecting this recipe. This message will be sent to the contributor.
              </Text>
            </View>
            <View style={styles.modalBody}>
              <TextInput
                style={styles.textArea}
                placeholder="Enter rejection reason..."
                value={rejectionMessage}
                onChangeText={setRejectionMessage}
                multiline
                numberOfLines={4}
                textAlignVertical="top"
              />
            </View>
            <View style={styles.modalFooter}>
              <Button
                variant="outline"
                onPress={() => setRejectionDialogOpen(false)}
                style={styles.modalButton}
              >
                Cancel
              </Button>
              <Button
                variant="destructive"
                onPress={handleRejectConfirm}
                disabled={!rejectionMessage.trim()}
                style={styles.modalButton}
              >
                Confirm Rejection
              </Button>
            </View>
          </View>
        </View>
      )}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.background,
  },
  loadingText: {
    marginTop: Spacing.md,
    fontSize: 16,
    color: Colors.textPrimary,
  },
  header: {
    padding: Spacing.lg,
    backgroundColor: Colors.card,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: Colors.textPrimary,
  },
  card: {
    margin: Spacing.lg,
  },
  cardTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: Spacing.xs,
  },
  cardDescription: {
    fontSize: 14,
    color: Colors.textSecondary,
  },
  emptyText: {
    fontSize: 16,
    color: Colors.textSecondary,
    textAlign: 'center',
    padding: Spacing.xl,
  },
  recipesList: {
    gap: Spacing.md,
  },
  recipeCard: {
    marginBottom: Spacing.md,
  },
  recipeTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: Spacing.xs,
  },
  recipeDescription: {
    fontSize: 14,
    color: Colors.textSecondary,
  },
  recipeContent: {
    gap: Spacing.md,
  },
  recipeInfo: {
    gap: Spacing.xs,
  },
  recipeDetail: {
    fontSize: 12,
    color: Colors.textMuted,
  },
  recipeActions: {
    flexDirection: 'row',
    gap: Spacing.sm,
  },
  actionButton: {
    flex: 1,
  },
  loadingButtonText: {
    marginLeft: Spacing.xs,
  },
  modalOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  modalContent: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    margin: Spacing.lg,
    maxWidth: 400,
    width: '90%',
  },
  modalHeader: {
    marginBottom: Spacing.lg,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: Spacing.sm,
  },
  modalDescription: {
    fontSize: 14,
    color: Colors.textSecondary,
  },
  modalBody: {
    marginBottom: Spacing.lg,
  },
  textArea: {
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: BorderRadius.md,
    padding: Spacing.md,
    fontSize: 16,
    backgroundColor: Colors.card,
    color: Colors.textPrimary,
    minHeight: 100,
    textAlignVertical: 'top',
  },
  modalFooter: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: Spacing.sm,
  },
  modalButton: {
    minWidth: 100,
  },
});