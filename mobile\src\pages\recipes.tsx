import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, ActivityIndicator } from 'react-native';
import { useQuery } from '@tanstack/react-query';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Input } from "../components/ui/input";
import { Button } from "../components/ui/button";
import { RecipeCard, AddRecipeCard } from "../components/recipes/recipe-card";
import { Colors } from "../lib/constants";
import { useAuth } from "../hooks/use-auth";
import { useToast } from "../hooks/use-toast";

interface Recipe {
  id: number;
  title: string;
  description: string;
  images: string[];
  tags: string[];
  contributor: {
    id: number;
    name: string;
  };
  project: {
    id: number;
    name: string;
  };
  status: string;
  createdAt: string;
}

const categories = ["All", "Main Dishes", "Desserts", "Vegetarian"];

export default function Recipes() {
  const [selectedCategory, setSelectedCategory] = useState(0);
  const [searchText, setSearchText] = useState('');
  const { user } = useAuth();
  const { toast } = useToast();

  // Fetch recipes from API
  const { data: recipesData, isLoading, isError } = useQuery({
    queryKey: ['recipes'],
    queryFn: async () => {
      const token = await AsyncStorage.getItem('token');
      if (!token) {
        throw new Error('Not authenticated');
      }

      // Fetch recipes based on user role
      let endpoint = '';
      if (user?.role === 'organizer') {
        endpoint = `${process.env.EXPO_PUBLIC_API_URL || 'http://localhost:3000'}/api/organizer/recipes`;
      } else if (user?.role === 'contributor') {
        endpoint = `${process.env.EXPO_PUBLIC_API_URL || 'http://localhost:3000'}/api/contributor/recipes`;
      } else if (user?.role === 'admin') {
        endpoint = `${process.env.EXPO_PUBLIC_API_URL || 'http://localhost:3000'}/api/admin/recipes`;
      } else {
        throw new Error('User role not supported');
      }

      const response = await fetch(endpoint, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch recipes');
      }

      const data = await response.json();
      return data.recipes || [];
    },
    enabled: !!user
  });

  const recipes = recipesData || [];

  // Filter recipes based on search and category
  const filteredRecipes = recipes.filter((recipe: Recipe) => {
    const matchesSearch = !searchText ||
      recipe.title.toLowerCase().includes(searchText.toLowerCase()) ||
      recipe.description.toLowerCase().includes(searchText.toLowerCase()) ||
      recipe.tags.some(tag => tag.toLowerCase().includes(searchText.toLowerCase()));

    const matchesCategory = selectedCategory === 0 ||
      (selectedCategory === 1 && recipe.tags.some(tag => tag.toLowerCase().includes('main'))) ||
      (selectedCategory === 2 && recipe.tags.some(tag => tag.toLowerCase().includes('dessert'))) ||
      (selectedCategory === 3 && recipe.tags.some(tag => tag.toLowerCase().includes('vegetarian')));

    return matchesSearch && matchesCategory;
  });

  const formatTags = (tags: string[]) => {
    return tags.slice(0, 3).map(tag => ({
      label: tag,
      variant: 'outline' as const
    }));
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>Recipes</Text>
        <Text style={styles.subtitle}>Browse and manage your recipe collection</Text>

        <View style={styles.searchSection}>
          <View style={styles.searchContainer}>
            <Input
              placeholder="Search recipes..."
              value={searchText}
              onChangeText={setSearchText}
              style={styles.searchInput}
            />
          </View>

          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.categoriesContainer}>
            <View style={styles.categories}>
              {categories.map((category, index) => (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.categoryButton,
                    selectedCategory === index && styles.selectedCategoryButton
                  ]}
                  onPress={() => setSelectedCategory(index)}
                >
                  <Text style={[
                    styles.categoryButtonText,
                    selectedCategory === index && styles.selectedCategoryButtonText
                  ]}>
                    {category}
                  </Text>
                </TouchableOpacity>
              ))}
              <TouchableOpacity style={styles.filterButton}>
                <Text style={styles.filterButtonText}>⚙ More</Text>
              </TouchableOpacity>
            </View>
          </ScrollView>
        </View>

        {isLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={Colors.primary} />
            <Text style={styles.loadingText}>Loading recipes...</Text>
          </View>
        ) : isError ? (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>Failed to load recipes</Text>
            <Button onPress={() => window.location.reload()} style={styles.retryButton}>
              <Text>Try Again</Text>
            </Button>
          </View>
        ) : (
          <View style={styles.recipesGrid}>
            {filteredRecipes.length > 0 ? (
              filteredRecipes.map((recipe: Recipe) => (
                <RecipeCard
                  key={recipe.id}
                  title={recipe.title}
                  description={recipe.description}
                  tags={formatTags(recipe.tags)}
                  contributor={recipe.contributor.name}
                  images={recipe.images}
                  isSaved={false} // TODO: Implement saved recipes functionality
                />
              ))
            ) : (
              <View style={styles.emptyContainer}>
                <Text style={styles.emptyText}>
                  {searchText || selectedCategory > 0
                    ? 'No recipes found matching your criteria.'
                    : 'No recipes available yet.'}
                </Text>
              </View>
            )}

            <AddRecipeCard />
          </View>
        )}
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  content: {
    padding: 16,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 8,
    fontFamily: 'serif',
    color: Colors.textPrimary,
  },
  subtitle: {
    fontSize: 18,
    color: Colors.textSecondary,
    marginBottom: 32,
  },
  searchSection: {
    marginBottom: 32,
  },
  searchContainer: {
    marginBottom: 16,
  },
  searchInput: {
    paddingLeft: 40,
  },
  categoriesContainer: {
    marginBottom: 16,
  },
  categories: {
    flexDirection: 'row',
    gap: 8,
    paddingRight: 16,
  },
  categoryButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: Colors.border,
    backgroundColor: Colors.card,
  },
  selectedCategoryButton: {
    backgroundColor: Colors.primary,
    borderColor: Colors.primary,
  },
  categoryButtonText: {
    fontSize: 14,
    color: Colors.textSecondary,
    fontWeight: '500',
  },
  selectedCategoryButtonText: {
    color: '#FFFFFF',
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: Colors.border,
    backgroundColor: Colors.card,
  },
  filterButtonText: {
    fontSize: 14,
    color: Colors.textSecondary,
    fontWeight: '500',
  },
  recipesGrid: {
    gap: 24,
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: Colors.textSecondary,
  },
  errorContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  errorText: {
    fontSize: 16,
    color: Colors.destructive,
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    marginTop: 8,
  },
  emptyContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  emptyText: {
    fontSize: 16,
    color: Colors.textSecondary,
    textAlign: 'center',
  },
});
