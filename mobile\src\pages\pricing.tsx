import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { PricingCalculator } from '../components/pricing/pricing-calculator';
import { Card } from '../components/ui/card';
import { Colors } from '../lib/constants';

// Pricing tiers matching the web version
const PricingTier = {
  SMALL: 'small',
  MEDIUM: 'medium',
  LARGE: 'large'
} as const;

const PricingModel = {
  [PricingTier.SMALL]: {
    name: 'Small',
    maxContributors: 1,
    minRecipes: 1,
    maxRecipes: 1,
    basePrice: 29.99,
    pricePerPage: 0.10,
    description: 'Perfect for small families (up to 1 contributor and 1 recipe for testing)'
  },
  [PricingTier.MEDIUM]: {
    name: 'Medium',
    maxContributors: 5,
    minRecipes: 6,
    maxRecipes: 15,
    basePrice: 49.99,
    pricePerPage: 0.08,
    description: 'Great for extended families (up to 5 contributors)'
  },
  [PricingTier.LARGE]: {
    name: 'Large',
    maxContributors: 10,
    minRecipes: 16,
    maxRecipes: 30,
    basePrice: 79.99,
    pricePerPage: 0.06,
    description: 'Ideal for large family reunions (up to 10 contributors)'
  }
};

const PAGE_COUNT_PER_RECIPE = 2;

export default function Pricing() {
  const [pricingInfo, setPricingInfo] = useState({
    tier: PricingTier.SMALL,
    basePrice: PricingModel[PricingTier.SMALL].basePrice,
    pagePrice: 0,
    totalPrice: PricingModel[PricingTier.SMALL].basePrice,
    estimatedPages: 20
  });

  return (
    <ScrollView style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>Pricing Calculator</Text>
        <Text style={styles.subtitle}>
          Estimate the cost of your cookbook based on size and number of recipes
        </Text>

        <View style={styles.mainContent}>
          <View style={styles.calculatorSection}>
            <PricingCalculator />
          </View>

          <View style={styles.infoSection}>
            <Card style={styles.card}>
              <View style={styles.cardContent}>
                <Text style={styles.cardTitle}>How Pricing Works</Text>
                <Text style={styles.cardDescription}>
                  Understanding our pricing model
                </Text>
                
                <Text style={styles.infoText}>
                  Our pricing is based on two factors:
                </Text>
                
                <View style={styles.bulletList}>
                  <View style={styles.bulletItem}>
                    <Text style={styles.bullet}>•</Text>
                    <Text style={styles.bulletText}>
                      <Text style={styles.bold}>Base Price:</Text> Determined by the tier you select, which limits the maximum number of contributors
                    </Text>
                  </View>
                  <View style={styles.bulletItem}>
                    <Text style={styles.bullet}>•</Text>
                    <Text style={styles.bulletText}>
                      <Text style={styles.bold}>Page Price:</Text> Based on the estimated number of pages in your final cookbook
                    </Text>
                  </View>
                </View>
                
                <Text style={styles.footnote}>
                  Each recipe typically takes about {PAGE_COUNT_PER_RECIPE} pages in the final cookbook.
                </Text>
              </View>
            </Card>

            <Card style={styles.card}>
              <View style={styles.cardContent}>
                <Text style={styles.cardTitle}>Tier Comparison</Text>
                <Text style={styles.cardDescription}>
                  Choose the right size for your cookbook
                </Text>
                
                <View style={styles.tierList}>
                  {Object.entries(PricingModel).map(([tier, data]) => (
                    <View key={tier} style={styles.tierItem}>
                      <Text style={styles.tierName}>{data.name}</Text>
                      <Text style={styles.tierDescription}>{data.description}</Text>
                      
                      <View style={styles.tierDetails}>
                        <View style={styles.tierDetailRow}>
                          <Text style={styles.tierDetailLabel}>Contributors:</Text>
                          <Text style={styles.tierDetailValue}>Max {data.maxContributors}</Text>
                        </View>
                        <View style={styles.tierDetailRow}>
                          <Text style={styles.tierDetailLabel}>Recipes:</Text>
                          <Text style={styles.tierDetailValue}>{data.minRecipes}-{data.maxRecipes}</Text>
                        </View>
                        <View style={styles.tierDetailRow}>
                          <Text style={styles.tierDetailLabel}>Base Price:</Text>
                          <Text style={styles.tierDetailValue}>${data.basePrice.toFixed(2)}</Text>
                        </View>
                        <View style={styles.tierDetailRow}>
                          <Text style={styles.tierDetailLabel}>Per Page:</Text>
                          <Text style={styles.tierDetailValue}>${data.pricePerPage.toFixed(2)}</Text>
                        </View>
                      </View>
                    </View>
                  ))}
                </View>
              </View>
            </Card>
          </View>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  content: {
    padding: 16,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 8,
    color: Colors.textPrimary,
    fontFamily: 'serif',
  },
  subtitle: {
    fontSize: 16,
    color: Colors.textSecondary,
    marginBottom: 32,
    lineHeight: 24,
  },
  mainContent: {
    gap: 32,
  },
  calculatorSection: {
    flex: 1,
  },
  infoSection: {
    gap: 24,
  },
  card: {
    marginBottom: 16,
  },
  cardContent: {
    padding: 16,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
    color: Colors.textPrimary,
  },
  cardDescription: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginBottom: 16,
  },
  infoText: {
    fontSize: 14,
    color: Colors.textPrimary,
    marginBottom: 12,
  },
  bulletList: {
    marginBottom: 16,
  },
  bulletItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  bullet: {
    fontSize: 14,
    color: Colors.textPrimary,
    marginRight: 8,
    marginTop: 2,
  },
  bulletText: {
    fontSize: 14,
    color: Colors.textPrimary,
    flex: 1,
    lineHeight: 20,
  },
  bold: {
    fontWeight: 'bold',
  },
  footnote: {
    fontSize: 12,
    color: Colors.textMuted,
    fontStyle: 'italic',
  },
  tierList: {
    gap: 16,
  },
  tierItem: {
    padding: 12,
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: 8,
    backgroundColor: Colors.muted,
  },
  tierName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: 4,
  },
  tierDescription: {
    fontSize: 12,
    color: Colors.textSecondary,
    marginBottom: 12,
  },
  tierDetails: {
    gap: 4,
  },
  tierDetailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  tierDetailLabel: {
    fontSize: 12,
    color: Colors.textSecondary,
  },
  tierDetailValue: {
    fontSize: 12,
    fontWeight: '500',
    color: Colors.textPrimary,
  },
});
