import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image } from 'react-native';
import { Card } from "../ui/card";
import { Button } from "../ui/button";
import { Colors } from "../../lib/constants";
import { RecipeImages } from "./recipe-images";

type Tag = {
  label: string;
  variant: "primary" | "secondary" | "outline";
};

type RecipeCardProps = {
  title: string;
  description: string;
  tags: Tag[];
  contributor: string;
  images?: string[];
  isSaved: boolean;
};

const mockImages = [
  'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?w=400',
  'https://images.unsplash.com/photo-1551782450-a2132b4ba21d?w=400',
  'https://images.unsplash.com/photo-1464349095431-e9a21285b5f3?w=400',
];

export function RecipeCard({
  title,
  description,
  tags,
  contributor,
  images = [],
  isSaved,
}: RecipeCardProps) {
  const [imageError, setImageError] = useState(false);
  const getTagStyle = (variant: string) => {
    switch (variant) {
      case 'primary':
        return { backgroundColor: Colors.muted, color: Colors.primary };
      case 'secondary':
        return { backgroundColor: Colors.muted, color: Colors.textSecondary };
      case 'outline':
        return { backgroundColor: Colors.card, color: Colors.textSecondary, borderWidth: 1, borderColor: Colors.border };
      default:
        return { backgroundColor: Colors.muted, color: Colors.textSecondary };
    }
  };

  return (
    <Card style={styles.card}>
      <View style={styles.imageContainer}>
        {images && images.length > 0 ? (
          <RecipeImages
            images={[images[0]]} // Show only the first image in card
            recipeTitle={title}
            style={styles.image}
          />
        ) : (
          <View style={[styles.image, styles.imagePlaceholder]}>
            <Text style={styles.placeholderText}>🍽️</Text>
            <Text style={styles.placeholderSubtext}>Recipe Image</Text>
          </View>
        )}
        <TouchableOpacity style={styles.saveButton}>
          <Text style={[styles.saveButtonText, isSaved && styles.savedText]}>
            {isSaved ? '♥' : '♡'}
          </Text>
        </TouchableOpacity>
      </View>

      <View style={styles.content}>
        <Text style={styles.title}>{title}</Text>
        <Text style={styles.description}>{description}</Text>

        <View style={styles.tags}>
          {tags.map((tag, index) => (
            <View key={index} style={[styles.tag, getTagStyle(tag.variant)]}>
              <Text style={[styles.tagText, { color: getTagStyle(tag.variant).color }]}>
                {tag.label}
              </Text>
            </View>
          ))}
        </View>

        <View style={styles.footer}>
          <Text style={styles.contributor}>by {contributor}</Text>
          <Button title="View Recipe" size="sm" />
        </View>
      </View>
    </Card>
  );
}

export function AddRecipeCard() {
  return (
    <Card style={[styles.card, styles.addRecipeCard]}>
      <View style={styles.addRecipeContent}>
        <View style={styles.addRecipeIcon}>
          <Text style={styles.addRecipeIconText}>+</Text>
        </View>
        <Text style={styles.addRecipeTitle}>Add New Recipe</Text>
        <Text style={styles.addRecipeSubtitle}>Share your family's favorite recipe</Text>
        <Button title="Add Recipe" />
      </View>
    </Card>
  );
}

const styles = StyleSheet.create({
  card: {
    marginBottom: 16,
    borderRadius: 8,
    overflow: 'hidden',
  },
  imageContainer: {
    position: 'relative',
    height: 200,
  },
  image: {
    width: '100%',
    height: '100%',
  },
  imagePlaceholder: {
    backgroundColor: Colors.muted,
    alignItems: 'center',
    justifyContent: 'center',
  },
  placeholderText: {
    fontSize: 32,
    marginBottom: 8,
  },
  placeholderSubtext: {
    fontSize: 12,
    color: Colors.textMuted,
  },
  saveButton: {
    position: 'absolute',
    top: 12,
    right: 12,
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  saveButtonText: {
    fontSize: 16,
    color: Colors.textMuted,
  },
  savedText: {
    color: Colors.destructive,
  },
  content: {
    padding: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
    fontFamily: 'serif',
    color: Colors.textPrimary,
  },
  description: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginBottom: 12,
    lineHeight: 20,
  },
  tags: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 16,
  },
  tag: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  tagText: {
    fontSize: 12,
    fontWeight: '500',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  contributor: {
    fontSize: 12,
    color: Colors.textMuted,
    fontStyle: 'italic',
  },
  addRecipeCard: {
    borderWidth: 2,
    borderStyle: 'dashed',
    borderColor: Colors.border,
    backgroundColor: Colors.muted,
  },
  addRecipeContent: {
    padding: 32,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 200,
  },
  addRecipeIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: Colors.muted,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  addRecipeIconText: {
    fontSize: 20,
    color: Colors.primary,
    fontWeight: 'bold',
  },
  addRecipeTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
    fontFamily: 'serif',
    color: Colors.textPrimary,
  },
  addRecipeSubtitle: {
    fontSize: 14,
    color: Colors.textSecondary,
    textAlign: 'center',
    marginBottom: 16,
  },
});
