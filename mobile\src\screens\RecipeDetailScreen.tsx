import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, ActivityIndicator, TouchableOpacity } from 'react-native';
import { useQuery } from '@tanstack/react-query';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Colors, Spacing, BorderRadius, API_URL } from '../lib/constants';
import { useAuth } from '../hooks/use-auth';
import { useToast } from '../hooks/use-toast';
import { Card, CardContent, CardHeader } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { RecipeImages } from '../components/recipes/recipe-images';
import { RecipeComments } from '../components/recipes/recipe-comments';
import Icon from 'react-native-vector-icons/Feather';

interface Recipe {
  id: number;
  title: string;
  description: string;
  images: string[];
  ingredients: { name: string; amount: number; unit: string }[];
  instructions: string[];
  prepTime?: number;
  cookTime?: number;
  servings?: number;
  difficulty?: string;
  tags: string[];
  createdAt: string;
  contributor: {
    id: number;
    name: string;
  };
  project: {
    id: number;
    name: string;
    organizerId: number;
  };
}

interface RecipeDetailScreenProps {
  recipeId: number;
}

export default function RecipeDetailScreen({ recipeId }: RecipeDetailScreenProps) {
  const [activeTab, setActiveTab] = useState<'recipe' | 'comments'>('recipe');
  const { user } = useAuth();
  const { toast } = useToast();

  // Fetch recipe details
  const { data: recipe, isLoading, isError } = useQuery({
    queryKey: ['recipe', recipeId],
    queryFn: async () => {
      const token = await AsyncStorage.getItem('token');
      if (!token) {
        throw new Error('Not authenticated');
      }

      const response = await fetch(`${API_URL}/recipes/${recipeId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch recipe');
      }

      const data = await response.json();
      return data.recipe as Recipe;
    }
  });

  const formatTime = (minutes?: number) => {
    if (!minutes) return 'N/A';
    if (minutes < 60) return `${minutes}m`;
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`;
  };

  const getDifficultyColor = (difficulty?: string) => {
    switch (difficulty?.toLowerCase()) {
      case 'easy': return '#22c55e';
      case 'medium': return '#f59e0b';
      case 'hard': return '#ef4444';
      default: return Colors.mutedForeground;
    }
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={Colors.primary} />
        <Text style={styles.loadingText}>Loading recipe...</Text>
      </View>
    );
  }

  if (isError || !recipe) {
    return (
      <View style={styles.errorContainer}>
        <Icon name="alert-circle" size={48} color={Colors.destructive} />
        <Text style={styles.errorText}>Failed to load recipe</Text>
        <Button onPress={() => window.location.reload()} style={styles.retryButton}>
          <Text>Try Again</Text>
        </Button>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      {/* Header */}
      <Card style={styles.headerCard}>
        <CardContent style={styles.headerContent}>
          <Text style={styles.title}>{recipe.title}</Text>
          <Text style={styles.description}>{recipe.description}</Text>

          {/* Recipe Meta */}
          <View style={styles.metaContainer}>
            {recipe.prepTime && (
              <View style={styles.metaItem}>
                <Icon name="clock" size={16} color={Colors.mutedForeground} />
                <Text style={styles.metaText}>Prep: {formatTime(recipe.prepTime)}</Text>
              </View>
            )}
            {recipe.cookTime && (
              <View style={styles.metaItem}>
                <Icon name="flame" size={16} color={Colors.mutedForeground} />
                <Text style={styles.metaText}>Cook: {formatTime(recipe.cookTime)}</Text>
              </View>
            )}
            {recipe.servings && (
              <View style={styles.metaItem}>
                <Icon name="users" size={16} color={Colors.mutedForeground} />
                <Text style={styles.metaText}>{recipe.servings} servings</Text>
              </View>
            )}
            {recipe.difficulty && (
              <View style={styles.metaItem}>
                <Icon name="bar-chart" size={16} color={getDifficultyColor(recipe.difficulty)} />
                <Text style={[styles.metaText, { color: getDifficultyColor(recipe.difficulty) }]}>
                  {recipe.difficulty}
                </Text>
              </View>
            )}
          </View>

          {/* Tags */}
          {recipe.tags && recipe.tags.length > 0 && (
            <View style={styles.tagsContainer}>
              {recipe.tags.map((tag, index) => (
                <View key={index} style={styles.tag}>
                  <Text style={styles.tagText}>{tag}</Text>
                </View>
              ))}
            </View>
          )}

          {/* Contributor Info */}
          <View style={styles.contributorInfo}>
            <Text style={styles.contributorText}>
              By {recipe.contributor.name} • {recipe.project.name}
            </Text>
            <Text style={styles.dateText}>
              Created {new Date(recipe.createdAt).toLocaleDateString()}
            </Text>
          </View>
        </CardContent>
      </Card>

      {/* Tabs */}
      <View style={styles.tabsContainer}>
        <View style={styles.tabsHeader}>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'recipe' && styles.activeTab]}
            onPress={() => setActiveTab('recipe')}
          >
            <Icon name="book-open" size={16} color={activeTab === 'recipe' ? Colors.primary : Colors.mutedForeground} />
            <Text style={[styles.tabText, activeTab === 'recipe' && styles.activeTabText]}>Recipe</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'comments' && styles.activeTab]}
            onPress={() => setActiveTab('comments')}
          >
            <Icon name="message-square" size={16} color={activeTab === 'comments' ? Colors.primary : Colors.mutedForeground} />
            <Text style={[styles.tabText, activeTab === 'comments' && styles.activeTabText]}>Comments</Text>
          </TouchableOpacity>
        </View>

        {/* Tab Content */}
        {activeTab === 'recipe' ? (
          <View style={styles.tabContent}>
            {/* Recipe Images */}
            {recipe.images && recipe.images.length > 0 && (
              <Card style={styles.sectionCard}>
                <CardContent style={styles.sectionContent}>
                  <RecipeImages
                    images={recipe.images}
                    recipeTitle={recipe.title}
                  />
                </CardContent>
              </Card>
            )}

            {/* Ingredients */}
            <Card style={styles.sectionCard}>
              <CardHeader style={styles.sectionHeader}>
                <View style={styles.sectionHeaderContent}>
                  <Icon name="list" size={20} color={Colors.primary} />
                  <Text style={styles.sectionTitle}>Ingredients ({recipe.ingredients.length})</Text>
                </View>
              </CardHeader>
              <CardContent style={styles.sectionContent}>
                {recipe.ingredients.map((ingredient, index) => (
                  <View key={index} style={styles.ingredientItem}>
                    <View style={styles.bullet} />
                    <Text style={styles.ingredientText}>
                      {ingredient.amount} {ingredient.unit} {ingredient.name}
                    </Text>
                  </View>
                ))}
              </CardContent>
            </Card>

            {/* Instructions */}
            <Card style={styles.sectionCard}>
              <CardHeader style={styles.sectionHeader}>
                <View style={styles.sectionHeaderContent}>
                  <Icon name="navigation" size={20} color={Colors.primary} />
                  <Text style={styles.sectionTitle}>Instructions ({recipe.instructions.length} steps)</Text>
                </View>
              </CardHeader>
              <CardContent style={styles.sectionContent}>
                {recipe.instructions.map((instruction, index) => (
                  <View key={index} style={styles.instructionItem}>
                    <View style={styles.stepNumber}>
                      <Text style={styles.stepNumberText}>{index + 1}</Text>
                    </View>
                    <Text style={styles.instructionText}>{instruction}</Text>
                  </View>
                ))}
              </CardContent>
            </Card>
          </View>
        ) : (
          <View style={styles.tabContent}>
            <Card style={styles.sectionCard}>
              <CardContent style={styles.sectionContent}>
                <RecipeComments recipeId={recipe.id} />
              </CardContent>
            </Card>
          </View>
        )}
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.background,
  },
  loadingText: {
    marginTop: Spacing.md,
    fontSize: 16,
    color: Colors.mutedForeground,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.background,
    padding: Spacing.xl,
  },
  errorText: {
    fontSize: 18,
    color: Colors.destructive,
    textAlign: 'center',
    marginVertical: Spacing.lg,
  },
  retryButton: {
    marginTop: Spacing.md,
  },
  headerCard: {
    margin: Spacing.lg,
    marginBottom: Spacing.md,
  },
  headerContent: {
    padding: Spacing.lg,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: Colors.foreground,
    marginBottom: Spacing.sm,
    lineHeight: 34,
  },
  description: {
    fontSize: 16,
    color: Colors.mutedForeground,
    marginBottom: Spacing.lg,
    lineHeight: 24,
  },
  metaContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: Spacing.md,
    marginBottom: Spacing.lg,
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.xs,
  },
  metaText: {
    fontSize: 14,
    color: Colors.mutedForeground,
    fontWeight: '500',
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: Spacing.sm,
    marginBottom: Spacing.lg,
  },
  tag: {
    backgroundColor: Colors.muted,
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.full,
  },
  tagText: {
    fontSize: 12,
    color: Colors.mutedForeground,
    fontWeight: '500',
  },
  contributorInfo: {
    borderTopWidth: 1,
    borderTopColor: Colors.border,
    paddingTop: Spacing.md,
  },
  contributorText: {
    fontSize: 14,
    color: Colors.foreground,
    fontWeight: '500',
    marginBottom: Spacing.xs,
  },
  dateText: {
    fontSize: 12,
    color: Colors.mutedForeground,
  },
  tabsContainer: {
    margin: Spacing.lg,
    marginTop: 0,
  },
  tabsHeader: {
    flexDirection: 'row',
    backgroundColor: Colors.muted,
    borderRadius: BorderRadius.lg,
    padding: Spacing.xs,
    marginBottom: Spacing.lg,
  },
  tab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: Spacing.xs,
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.lg,
    borderRadius: BorderRadius.md,
  },
  activeTab: {
    backgroundColor: Colors.card,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  tabText: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.mutedForeground,
  },
  activeTabText: {
    color: Colors.foreground,
  },
  tabContent: {
    gap: Spacing.lg,
  },
  sectionCard: {
    marginBottom: Spacing.md,
  },
  sectionHeader: {
    backgroundColor: Colors.muted + '50',
  },
  sectionHeaderContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.sm,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.foreground,
  },
  sectionContent: {
    padding: Spacing.lg,
    overflow: 'hidden', // Prevent content overflow
  },
  recipeImage: {
    width: '100%',
    height: 200,
    borderRadius: BorderRadius.lg,
    marginBottom: Spacing.sm,
  },
  ingredientItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: Spacing.sm,
    gap: Spacing.sm,
  },
  bullet: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: Colors.primary,
    marginTop: 8,
  },
  ingredientText: {
    fontSize: 16,
    color: Colors.foreground,
    lineHeight: 24,
    flex: 1,
  },
  instructionItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: Spacing.lg,
    gap: Spacing.md,
  },
  stepNumber: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 2,
  },
  stepNumberText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: Colors.primaryForeground,
  },
  instructionText: {
    fontSize: 16,
    color: Colors.foreground,
    lineHeight: 24,
    flex: 1,
  },
});
