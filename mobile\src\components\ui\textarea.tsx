import React from 'react';
import { TextInput, StyleSheet, TextInputProps, ViewStyle } from 'react-native';
import { Colors } from '../../lib/constants';

interface TextareaProps extends TextInputProps {
  style?: ViewStyle;
}

export function Textarea({ style, ...props }: TextareaProps) {
  return (
    <TextInput
      {...props}
      multiline
      textAlignVertical="top"
      style={[styles.textarea, style]}
      placeholderTextColor={Colors.textMuted}
    />
  );
}

const styles = StyleSheet.create({
  textarea: {
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: 6,
    padding: 12,
    fontSize: 14,
    backgroundColor: Colors.card,
    minHeight: 80,
    color: Colors.textPrimary,
  },
});
