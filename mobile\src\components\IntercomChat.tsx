import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert } from 'react-native';
import { useAuth } from '../hooks/use-auth';
import { Colors, Spacing, BorderRadius } from '../lib/constants';
import Icon from 'react-native-vector-icons/Feather';

// For React Native, we'll create a functional component that integrates with Intercom
// In a real implementation, you would use @intercom/intercom-react-native

interface IntercomChatProps {
  appId?: string;
}

export function IntercomChat({ appId }: IntercomChatProps) {
  const { user } = useAuth();
  const [isLoaded, setIsLoaded] = useState(false);
  const [isIntercomAvailable, setIsIntercomAvailable] = useState(false);

  useEffect(() => {
    // Check if Intercom is configured
    const checkIntercomConfig = () => {
      // In a real implementation, this would check for the Intercom app ID and SDK
      const hasAppId = appId || process.env.EXPO_PUBLIC_INTERCOM_APP_ID;
      const hasIntercomSDK = false; // Would check if @intercom/intercom-react-native is available

      setIsIntercomAvailable(!!hasAppId);
      setIsLoaded(true);

      if (hasAppId && user) {
        console.log('Intercom would be initialized for mobile with:', {
          appId: hasAppId,
          userId: user.id,
          email: user.email,
          name: user.name
        });

        // In a real implementation, you would initialize Intercom here:
        // import Intercom from '@intercom/intercom-react-native';
        // Intercom.loginUserWithUserAttributes({
        //   userId: user.id.toString(),
        //   email: user.email,
        //   name: user.name
        // });
      }
    };

    checkIntercomConfig();
  }, [user, appId]);

  const openIntercom = () => {
    if (isIntercomAvailable) {
      // In a real implementation, this would open the Intercom messenger:
      // import Intercom from '@intercom/intercom-react-native';
      // Intercom.present();

      Alert.alert(
        'Intercom Chat',
        'Intercom chat would open here. In a real implementation, this would use the @intercom/intercom-react-native SDK.',
        [{ text: 'OK' }]
      );
    } else {
      Alert.alert(
        'Chat Unavailable',
        'Live chat is not configured. Please contact support through other means.',
        [{ text: 'OK' }]
      );
    }
  };

  // If Intercom is not available, don't render anything
  if (!isLoaded || !isIntercomAvailable) {
    return null;
  }

  // Render a floating chat button
  return (
    <View style={styles.container}>
      <TouchableOpacity style={styles.chatButton} onPress={openIntercom}>
        <Icon name="message-circle" size={24} color={Colors.primaryForeground} />
      </TouchableOpacity>
    </View>
  );
}

// Hook to check if Intercom should be enabled
export function useIntercomChat() {
  const [isEnabled, setIsEnabled] = useState(false);

  useEffect(() => {
    // Check if Intercom is configured
    const hasAppId = process.env.EXPO_PUBLIC_INTERCOM_APP_ID;
    setIsEnabled(!!hasAppId);
  }, []);

  return isEnabled;
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    zIndex: 1000,
  },
  chatButton: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: Colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
});

// Utility functions for Intercom API (mobile placeholders)
export const IntercomAPI = {
  // Show the messenger
  show: () => {
    console.log('Intercom show called (mobile)');
  },

  // Hide the messenger
  hide: () => {
    console.log('Intercom hide called (mobile)');
  },

  // Show a specific message
  showNewMessage: (message?: string) => {
    console.log('Intercom showNewMessage called (mobile):', message);
  },

  // Track an event
  trackEvent: (eventName: string, metadata?: Record<string, any>) => {
    console.log('Intercom trackEvent called (mobile):', eventName, metadata);
  },

  // Update user information
  updateUser: (userData: Record<string, any>) => {
    console.log('Intercom updateUser called (mobile):', userData);
  },

  // Get unread conversation count
  getUnreadCount: () => {
    return 0;
  }
};
