import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  Alert,
  ActivityIndicator
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useLocation } from '../lib/router';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useToast } from '../hooks/use-toast';
import { useAuth } from '../hooks/use-auth';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Colors, Spacing, BorderRadius, API_URL } from '../lib/constants';
import { Picker } from '@react-native-picker/picker';
import Icon from 'react-native-vector-icons/MaterialIcons';

// Pricing tiers
enum PricingTier {
  SMALL = 'small',
  MEDIUM = 'medium',
  LARGE = 'large'
}

// Pricing Model
const PricingModel = {
  [PricingTier.SMALL]: {
    name: 'Small',
    maxContributors: 1,
    minRecipes: 1,
    maxRecipes: 1,
    basePrice: 29.99,
    pricePerPage: 0.10,
    description: 'Perfect for small families (up to 1 contributor and 1 recipe for testing)'
  },
  [PricingTier.MEDIUM]: {
    name: 'Medium',
    maxContributors: 5,
    minRecipes: 6,
    maxRecipes: 15,
    basePrice: 49.99,
    pricePerPage: 0.08,
    description: 'Great for extended families (up to 5 contributors)'
  },
  [PricingTier.LARGE]: {
    name: 'Large',
    maxContributors: 10,
    minRecipes: 16,
    maxRecipes: 30,
    basePrice: 79.99,
    pricePerPage: 0.06,
    description: 'Ideal for large family reunions (up to 10 contributors)'
  }
};

export default function CreateRecipeBookScreen() {
  const [, setLocation] = useLocation();
  const { toast } = useToast();
  const { user } = useAuth();
  const queryClient = useQueryClient();

  // Form state
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [pricingTier, setPricingTier] = useState(PricingTier.SMALL);
  const [maxContributors, setMaxContributors] = useState(PricingModel[PricingTier.SMALL].maxContributors);

  // Update max contributors when pricing tier changes
  React.useEffect(() => {
    setMaxContributors(PricingModel[pricingTier].maxContributors);
  }, [pricingTier]);

  const { mutate: createRecipeBook, isPending } = useMutation({
    mutationFn: async (data: {
      title: string;
      description: string;
      pricingTier: string;
      maxContributors: number;
    }) => {
      console.log('Starting recipe book creation with data:', data);
      const token = await AsyncStorage.getItem('token');
      if (!token) {
        throw new Error('Not authenticated');
      }

      // Only allow organizers and admins to create recipe books
      if (user?.role !== 'organizer' && user?.role !== 'admin') {
        throw new Error('Only organizers and admins can create recipe books');
      }

      const endpoint = `${API_URL}/organizer/projects`;

      const response = await fetch(endpoint, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${token}`,
        },
        body: JSON.stringify({
          name: data.title,
          description: data.description,
          pricingTier: data.pricingTier,
          maxContributors: data.maxContributors,
        }),
      });

      console.log('Recipe book creation response status:', response.status);

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to create recipe book");
      }

      const result = await response.json();
      console.log('Recipe book creation response:', result);
      return result;
    },
    onSuccess: () => {
      console.log('Recipe book created successfully, redirecting...');
      // Invalidate the recipe books query to trigger a refetch
      queryClient.invalidateQueries({ queryKey: ["recipeBooks"] });
      toast({
        title: "Success",
        description: "Recipe book created successfully",
      });
      setLocation("/recipe-books");
    },
    onError: (error: Error) => {
      console.error('Error creating recipe book:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message,
      });
    },
  });

  const handleSubmit = () => {
    if (!title.trim() || !description.trim()) {
      Alert.alert('Error', 'Please fill in all required fields');
      return;
    }

    createRecipeBook({
      title,
      description,
      pricingTier,
      maxContributors
    });
  };

  // Check if user can create recipe books
  if (!user || (user.role !== 'organizer' && user.role !== 'admin')) {
    return (
      <View style={styles.container}>
        <Card style={styles.errorCard}>
          <CardContent>
            <Icon name="error" size={48} color={Colors.destructive} style={styles.errorIcon} />
            <Text style={styles.errorText}>
              Only organizers and admins can create recipe books.
            </Text>
            <Button
              onPress={() => setLocation("/recipe-books")}
              style={styles.backButton}
            >
              Back to Recipe Books
            </Button>
          </CardContent>
        </Card>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>Create New Recipe Book</Text>
        <Button
          variant="outline"
          onPress={() => setLocation("/recipe-books")}
          size="sm"
        >
          Back to Recipe Books
        </Button>
      </View>

      <Card style={styles.formCard}>
        <CardHeader>
          <Text style={styles.formTitle}>Recipe Book Details</Text>
          <Text style={styles.formDescription}>
            Create a new recipe book to collect and organize your family recipes
          </Text>
        </CardHeader>
        <CardContent>
          {/* Basic Information */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Title *</Text>
            <Input
              value={title}
              onChangeText={setTitle}
              placeholder="Enter recipe book title"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Description *</Text>
            <TextInput
              style={styles.textArea}
              value={description}
              onChangeText={setDescription}
              placeholder="Enter recipe book description"
              multiline
              numberOfLines={4}
              textAlignVertical="top"
            />
          </View>

          {/* Pricing Tier Selection */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Pricing Tier</Text>
            <View style={styles.pickerContainer}>
              <Picker
                selectedValue={pricingTier}
                onValueChange={setPricingTier}
                style={styles.picker}
              >
                {Object.entries(PricingModel).map(([key, tier]) => (
                  <Picker.Item
                    key={key}
                    label={`${tier.name} - $${tier.basePrice} (${tier.description})`}
                    value={key}
                  />
                ))}
              </Picker>
            </View>
          </View>

          {/* Pricing Information */}
          <Card style={styles.pricingCard}>
            <CardContent>
              <Text style={styles.pricingTitle}>
                {PricingModel[pricingTier].name} Plan
              </Text>
              <Text style={styles.pricingDescription}>
                {PricingModel[pricingTier].description}
              </Text>

              <View style={styles.pricingDetails}>
                <View style={styles.pricingItem}>
                  <Icon name="people" size={16} color={Colors.primary} />
                  <Text style={styles.pricingItemText}>
                    Up to {PricingModel[pricingTier].maxContributors} contributor{PricingModel[pricingTier].maxContributors !== 1 ? 's' : ''}
                  </Text>
                </View>
                <View style={styles.pricingItem}>
                  <Icon name="book" size={16} color={Colors.primary} />
                  <Text style={styles.pricingItemText}>
                    {PricingModel[pricingTier].minRecipes}-{PricingModel[pricingTier].maxRecipes} recipes
                  </Text>
                </View>
                <View style={styles.pricingItem}>
                  <Icon name="attach-money" size={16} color={Colors.primary} />
                  <Text style={styles.pricingItemText}>
                    Base price: ${PricingModel[pricingTier].basePrice}
                  </Text>
                </View>
                <View style={styles.pricingItem}>
                  <Icon name="description" size={16} color={Colors.primary} />
                  <Text style={styles.pricingItemText}>
                    ${PricingModel[pricingTier].pricePerPage} per additional page
                  </Text>
                </View>
              </View>
            </CardContent>
          </Card>

          {/* Form Actions */}
          <View style={styles.formActions}>
            <Button
              onPress={handleSubmit}
              disabled={isPending}
              style={styles.submitButton}
            >
              {isPending ? (
                <>
                  <ActivityIndicator size="small" color={Colors.primaryForeground} />
                  <Text style={styles.submitButtonText}>Creating...</Text>
                </>
              ) : (
                'Create Recipe Book'
              )}
            </Button>
            <Button
              variant="outline"
              onPress={() => setLocation("/recipe-books")}
              style={styles.cancelButton}
            >
              Cancel
            </Button>
          </View>
        </CardContent>
      </Card>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  errorCard: {
    margin: Spacing.lg,
    alignItems: 'center',
  },
  errorIcon: {
    marginBottom: Spacing.md,
  },
  errorText: {
    fontSize: 16,
    color: Colors.destructive,
    textAlign: 'center',
    marginBottom: Spacing.lg,
  },
  backButton: {
    marginTop: Spacing.md,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: Spacing.lg,
    backgroundColor: Colors.card,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: Colors.foreground,
    flex: 1,
  },
  formCard: {
    margin: Spacing.lg,
    marginBottom: Spacing.md,
  },
  formTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.cardForeground,
  },
  formDescription: {
    fontSize: 14,
    color: Colors.mutedForeground,
    marginTop: Spacing.xs,
  },
  inputGroup: {
    marginBottom: Spacing.lg,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.foreground,
    marginBottom: Spacing.sm,
  },
  textArea: {
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: BorderRadius.md,
    padding: Spacing.md,
    fontSize: 16,
    backgroundColor: Colors.card,
    color: Colors.foreground,
    minHeight: 100,
    textAlignVertical: 'top',
  },
  pickerContainer: {
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: BorderRadius.md,
    backgroundColor: Colors.card,
  },
  picker: {
    color: Colors.foreground,
  },
  pricingCard: {
    backgroundColor: Colors.muted,
    marginBottom: Spacing.lg,
  },
  pricingTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.foreground,
    marginBottom: Spacing.sm,
  },
  pricingDescription: {
    fontSize: 14,
    color: Colors.mutedForeground,
    marginBottom: Spacing.lg,
  },
  pricingDetails: {
    gap: Spacing.md,
  },
  pricingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.sm,
  },
  pricingItemText: {
    fontSize: 14,
    color: Colors.foreground,
    flex: 1,
  },
  formActions: {
    gap: Spacing.md,
    marginTop: Spacing.lg,
  },
  submitButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: Spacing.xs,
  },
  submitButtonText: {
    color: Colors.primaryForeground,
    marginLeft: Spacing.xs,
  },
  cancelButton: {
    marginBottom: 0,
  },
});
