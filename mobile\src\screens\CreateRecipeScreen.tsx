import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  ActivityIndicator
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useLocation } from '../lib/router';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useToast } from '../hooks/use-toast';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { useAuth } from '../hooks/use-auth';
import { Colors, Spacing, BorderRadius, API_URL } from '../lib/constants';
import { Picker } from '@react-native-picker/picker';
import { ImageUpload } from '../components/ui/image-upload';
import Icon from 'react-native-vector-icons/MaterialIcons';

interface Recipe {
  id: number;
  projectId: number;
  title: string;
  description: string;
  category: string;
  ingredients: Array<{
    name: string;
    amount: number;
    unit: string;
  }>;
  instructions: string[];
  tags: string[];
  measurementSystem: 'us' | 'metric';
  images: string[];
  status: string;
  createdAt: string;
  updatedAt: string;
}

interface Project {
  id: number;
  name: string;
  organizerId: number;
  pricingTier?: string;
  maxRecipes?: number;
  recipes?: any[];
}

interface RecipeFormData {
  projectId: number;
  title: string;
  description: string;
  category: string;
  ingredients: Array<{
    name: string;
    amount: number;
    unit: string;
  }>;
  instructions: string[];
  tags: string[];
  measurementSystem: 'us' | 'metric';
  images: string[];
}

const categories = [
  "Main Dishes",
  "Side Dishes",
  "Soups",
  "Salads",
  "Desserts",
  "Breakfast",
  "Snacks",
  "Beverages",
  "Appetizers",
  "Breads",
  "Sauces",
  "Other"
];

const commonIngredients = [
  "Salt", "Pepper", "Sugar", "Flour", "Butter", "Oil", "Garlic", "Onion",
  "Eggs", "Milk", "Cheese", "Tomatoes", "Potatoes", "Rice", "Pasta"
];

export default function CreateRecipeScreen() {
  const [, setLocation] = useLocation();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [isCreating, setIsCreating] = useState(false);
  const [activeTab, setActiveTab] = useState('manual');
  const { user } = useAuth();

  // Form state
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [category, setCategory] = useState('Main Dishes');
  const [selectedProjectId, setSelectedProjectId] = useState<number | null>(null);
  const [ingredients, setIngredients] = useState([{ name: '', amount: 1, unit: 'cup' }]);
  const [instructions, setInstructions] = useState(['']);
  const [tags, setTags] = useState('');
  const [measurementSystem, setMeasurementSystem] = useState<'us' | 'metric'>('us');
  const [selectedImages, setSelectedImages] = useState<string[]>([]);

  // Fetch recipe books
  const { data: projects, isLoading: isLoadingProjects } = useQuery<Project[]>({
    queryKey: ['projects', user?.role],
    queryFn: async () => {
      const token = await AsyncStorage.getItem('token');
      if (!token) {
        throw new Error('Not authenticated');
      }

      // Use different endpoints based on user role
      let endpoint = '';
      if (user?.role === 'organizer') {
        endpoint = `${API_URL}/organizer/my-projects`;
      } else if (user?.role === 'contributor') {
        endpoint = `${API_URL}/contributor/projects`;
      } else if (user?.role === 'admin') {
        endpoint = `${API_URL}/organizer/all-projects`;
      } else {
        throw new Error('User role not supported');
      }

      const response = await fetch(endpoint, {
        headers: {
          "Authorization": `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to fetch recipe books');
      }

      const data = await response.json();
      return data.projects || [];
    },
    enabled: !!user?.role
  });

  const isLoading = isLoadingProjects;

  // Helper functions for managing ingredients and instructions
  const addIngredient = () => {
    if (ingredients.length < 15) {
      setIngredients([...ingredients, { name: '', amount: 1, unit: 'cup' }]);
    }
  };

  const removeIngredient = (index: number) => {
    if (ingredients.length > 1) {
      setIngredients(ingredients.filter((_, i) => i !== index));
    }
  };

  const updateIngredient = (index: number, field: string, value: any) => {
    const updated = ingredients.map((ingredient, i) =>
      i === index ? { ...ingredient, [field]: value } : ingredient
    );
    setIngredients(updated);
  };

  const addInstruction = () => {
    if (instructions.length < 15) {
      setInstructions([...instructions, '']);
    }
  };

  const removeInstruction = (index: number) => {
    if (instructions.length > 1) {
      setInstructions(instructions.filter((_, i) => i !== index));
    }
  };

  const updateInstruction = (index: number, value: string) => {
    const updated = instructions.map((instruction, i) =>
      i === index ? value : instruction
    );
    setInstructions(updated);
  };

  const handleImageUpload = (urls: string[]) => {
    setSelectedImages(urls);
  };

  // Submit recipe mutation
  const submitRecipeMutation = useMutation({
    mutationFn: async (data: RecipeFormData) => {
      const token = await AsyncStorage.getItem('token');
      if (!token) {
        throw new Error('Not authenticated');
      }

      const response = await fetch(`${API_URL}/contributor/recipes`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          ...data,
          images: selectedImages,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to submit recipe');
      }

      const result = await response.json();
      queryClient.invalidateQueries({ queryKey: ['recipes'] });
      return result;
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Recipe submitted successfully",
      });
      setIsCreating(false);
      resetForm();
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to submit recipe",
        variant: "destructive",
      });
    },
  });

  const resetForm = () => {
    setTitle('');
    setDescription('');
    setCategory('Main Dishes');
    setSelectedProjectId(null);
    setIngredients([{ name: '', amount: 1, unit: 'cup' }]);
    setInstructions(['']);
    setTags('');
    setMeasurementSystem('us');
    setSelectedImages([]);
  };

  const handleSubmit = () => {
    if (!title.trim() || !description.trim() || !selectedProjectId) {
      Alert.alert('Error', 'Please fill in all required fields and select a recipe book');
      return;
    }

    const validIngredients = ingredients.filter(ing => ing.name.trim());
    const validInstructions = instructions.filter(inst => inst.trim());

    if (validIngredients.length === 0 || validInstructions.length === 0) {
      Alert.alert('Error', 'Please add at least one ingredient and one instruction');
      return;
    }

    const formData: RecipeFormData = {
      projectId: selectedProjectId,
      title: title.trim(),
      description: description.trim(),
      category,
      ingredients: validIngredients,
      instructions: validInstructions,
      tags: tags.split(',').map(tag => tag.trim()).filter(Boolean),
      measurementSystem,
      images: selectedImages,
    };

    submitRecipeMutation.mutate(formData);
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={Colors.primary} />
        <Text style={styles.loadingText}>Loading...</Text>
      </View>
    );
  }

  if (!projects || projects.length === 0) {
    return (
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>Create Recipe</Text>
        </View>
        <View style={styles.centerContent}>
          {user?.role === 'contributor' ? (
            <Text style={styles.emptyText}>You haven't been invited to contribute to any recipe books yet.</Text>
          ) : (
            <>
              <Text style={styles.emptyText}>You need to create a recipe book first before adding recipes.</Text>
              <Button
                onPress={() => setLocation("/recipe-books/create")}
                style={styles.createBookButton}
              >
                Create Recipe Book
              </Button>
            </>
          )}
        </View>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Create Recipe</Text>
        <Button
          onPress={() => setIsCreating(true)}
          disabled={isCreating}
          style={styles.addButton}
        >
          <Icon name="add" size={16} color={Colors.primaryForeground} />
          <Text style={styles.addButtonText}>Add New Recipe</Text>
        </Button>
      </View>

      {isCreating && (
        <Card style={styles.formCard}>
          <CardHeader>
            <Text style={styles.formTitle}>Create New Recipe</Text>
          </CardHeader>
          <CardContent>
            {/* Tabs for Manual Entry, Scan, Voice */}
            <View style={styles.tabContainer}>
              <TouchableOpacity
                style={[styles.tab, activeTab === 'manual' && styles.activeTab]}
                onPress={() => setActiveTab('manual')}
              >
                <Text style={[styles.tabText, activeTab === 'manual' && styles.activeTabText]}>
                  Manual Entry
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.tab, activeTab === 'scan' && styles.activeTab]}
                onPress={() => setActiveTab('scan')}
              >
                <Icon name="camera-alt" size={16} color={activeTab === 'scan' ? Colors.primaryForeground : Colors.foreground} />
                <Text style={[styles.tabText, activeTab === 'scan' && styles.activeTabText]}>
                  Scan Recipe
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.tab, activeTab === 'voice' && styles.activeTab]}
                onPress={() => setActiveTab('voice')}
              >
                <Icon name="mic" size={16} color={activeTab === 'voice' ? Colors.primaryForeground : Colors.foreground} />
                <Text style={[styles.tabText, activeTab === 'voice' && styles.activeTabText]}>
                  Voice Record
                </Text>
              </TouchableOpacity>
            </View>

            {activeTab === 'manual' && (
              <>
                {/* Recipe Book Selection */}
                <View style={styles.inputGroup}>
                  <Text style={styles.label}>Recipe Book *</Text>
                  <View style={styles.pickerContainer}>
                    <Picker
                      selectedValue={selectedProjectId || ''}
                      onValueChange={(value) => setSelectedProjectId(Number(value) || null)}
                      style={styles.picker}
                    >
                      <Picker.Item label="Select a recipe book" value="" />
                      {projects?.map((project) => (
                        <Picker.Item key={project.id} label={project.name} value={project.id} />
                      ))}
                    </Picker>
                  </View>
                </View>

                {/* Basic Info */}
                <View style={styles.inputGroup}>
                  <Text style={styles.label}>Title *</Text>
                  <Input
                    value={title}
                    onChangeText={setTitle}
                    placeholder="Enter recipe title"
                  />
                </View>

                <View style={styles.inputGroup}>
                  <Text style={styles.label}>Description *</Text>
                  <TextInput
                    style={styles.textArea}
                    value={description}
                    onChangeText={setDescription}
                    placeholder="Enter recipe description"
                    multiline
                    numberOfLines={3}
                    textAlignVertical="top"
                  />
                </View>

                <View style={styles.inputGroup}>
                  <Text style={styles.label}>Category *</Text>
                  <View style={styles.pickerContainer}>
                    <Picker
                      selectedValue={category}
                      onValueChange={setCategory}
                      style={styles.picker}
                    >
                      {categories.map((cat) => (
                        <Picker.Item key={cat} label={cat} value={cat} />
                      ))}
                    </Picker>
                  </View>
                </View>

                {/* Measurement System */}
                <View style={styles.inputGroup}>
                  <Text style={styles.label}>Measurement System</Text>
                  <View style={styles.measurementContainer}>
                    <TouchableOpacity
                      style={[styles.measurementButton, measurementSystem === 'us' && styles.measurementButtonActive]}
                      onPress={() => setMeasurementSystem('us')}
                    >
                      <Text style={[styles.measurementButtonText, measurementSystem === 'us' && styles.measurementButtonTextActive]}>
                        US (cups, oz, lbs)
                      </Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                      style={[styles.measurementButton, measurementSystem === 'metric' && styles.measurementButtonActive]}
                      onPress={() => setMeasurementSystem('metric')}
                    >
                      <Text style={[styles.measurementButtonText, measurementSystem === 'metric' && styles.measurementButtonTextActive]}>
                        Metric (ml, g, kg)
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>

                <View style={styles.inputGroup}>
                  <Text style={styles.label}>Tags (comma separated)</Text>
                  <Input
                    value={tags}
                    onChangeText={setTags}
                    placeholder="e.g. vegetarian, quick, healthy"
                  />
                </View>

                {/* Ingredients Section */}
                <View style={styles.section}>
                  <View style={styles.sectionHeader}>
                    <Text style={styles.sectionTitle}>Ingredients *</Text>
                    <Button
                      variant="outline"
                      size="sm"
                      onPress={addIngredient}
                      disabled={ingredients.length >= 15}
                    >
                      <Icon name="add" size={16} color={Colors.foreground} />
                      <Text style={styles.addButtonSmallText}>Add</Text>
                    </Button>
                  </View>
                  {ingredients.map((ingredient, index) => (
                    <View key={index} style={styles.ingredientRow}>
                      <View style={styles.ingredientName}>
                        <Input
                          value={ingredient.name}
                          onChangeText={(text) => updateIngredient(index, 'name', text)}
                          placeholder="Ingredient name"
                        />
                      </View>
                      <View style={styles.ingredientAmount}>
                        <Input
                          value={ingredient.amount.toString()}
                          onChangeText={(text) => updateIngredient(index, 'amount', parseFloat(text) || 0)}
                          keyboardType="numeric"
                          placeholder="1"
                        />
                      </View>
                      <View style={styles.ingredientUnit}>
                        <View style={styles.pickerContainer}>
                          <Picker
                            selectedValue={ingredient.unit}
                            onValueChange={(value) => updateIngredient(index, 'unit', value)}
                            style={styles.pickerSmall}
                          >
                            <Picker.Item label="cup" value="cup" />
                            <Picker.Item label="tbsp" value="tbsp" />
                            <Picker.Item label="tsp" value="tsp" />
                            <Picker.Item label="oz" value="oz" />
                            <Picker.Item label="lb" value="lb" />
                            <Picker.Item label="g" value="g" />
                            <Picker.Item label="kg" value="kg" />
                            <Picker.Item label="ml" value="ml" />
                            <Picker.Item label="l" value="l" />
                            <Picker.Item label="piece" value="piece" />
                            <Picker.Item label="clove" value="clove" />
                            <Picker.Item label="pinch" value="pinch" />
                          </Picker>
                        </View>
                      </View>
                      {ingredients.length > 1 && (
                        <TouchableOpacity
                          onPress={() => removeIngredient(index)}
                          style={styles.removeButton}
                        >
                          <Icon name="remove" size={20} color={Colors.destructive} />
                        </TouchableOpacity>
                      )}
                    </View>
                  ))}
                </View>

                {/* Instructions Section */}
                <View style={styles.section}>
                  <View style={styles.sectionHeader}>
                    <Text style={styles.sectionTitle}>Instructions *</Text>
                    <Button
                      variant="outline"
                      size="sm"
                      onPress={addInstruction}
                      disabled={instructions.length >= 15}
                    >
                      <Icon name="add" size={16} color={Colors.foreground} />
                      <Text style={styles.addButtonSmallText}>Add</Text>
                    </Button>
                  </View>
                  {instructions.map((instruction, index) => (
                    <View key={index} style={styles.instructionRow}>
                      <View style={styles.stepNumber}>
                        <Text style={styles.stepNumberText}>{index + 1}</Text>
                      </View>
                      <View style={styles.instructionInput}>
                        <TextInput
                          style={styles.instructionTextArea}
                          value={instruction}
                          onChangeText={(text) => updateInstruction(index, text)}
                          placeholder={`Step ${index + 1} instructions`}
                          multiline
                          numberOfLines={2}
                          textAlignVertical="top"
                        />
                      </View>
                      {instructions.length > 1 && (
                        <TouchableOpacity
                          onPress={() => removeInstruction(index)}
                          style={styles.removeButton}
                        >
                          <Icon name="remove" size={20} color={Colors.destructive} />
                        </TouchableOpacity>
                      )}
                    </View>
                  ))}
                </View>

                {/* Recipe Images */}
                <View style={styles.inputGroup}>
                  <Text style={styles.label}>Recipe Images</Text>
                  <ImageUpload
                    onUpload={handleImageUpload}
                    maxFiles={3}
                    acceptedFileTypes={['image/jpeg', 'image/png', 'image/webp']}
                    initialFiles={selectedImages}
                  />
                </View>

                {/* Form Actions */}
                <View style={styles.formActions}>
                  <Button
                    onPress={handleSubmit}
                    disabled={submitRecipeMutation.isPending}
                    style={styles.submitButton}
                  >
                    {submitRecipeMutation.isPending ? (
                      <>
                        <ActivityIndicator size="small" color={Colors.primaryForeground} />
                        <Text style={styles.submitButtonText}>Submitting...</Text>
                      </>
                    ) : (
                      'Submit Recipe'
                    )}
                  </Button>
                  <Button
                    variant="outline"
                    onPress={() => {
                      setIsCreating(false);
                      resetForm();
                    }}
                    style={styles.cancelButton}
                  >
                    Cancel
                  </Button>
                </View>
              </>
            )}

            {activeTab === 'scan' && (
              <View style={styles.scanContainer}>
                <View style={styles.inputGroup}>
                  <Text style={styles.label}>Recipe Book *</Text>
                  <View style={styles.pickerContainer}>
                    <Picker
                      selectedValue={selectedProjectId || ''}
                      onValueChange={(value) => setSelectedProjectId(Number(value) || null)}
                      style={styles.picker}
                    >
                      <Picker.Item label="Select a recipe book" value="" />
                      {projects?.map((project) => (
                        <Picker.Item key={project.id} label={project.name} value={project.id} />
                      ))}
                    </Picker>
                  </View>
                </View>

                <View style={styles.inputGroup}>
                  <Text style={styles.label}>Upload Recipe Image</Text>
                  <ImageUpload
                    onUpload={handleImageUpload}
                    maxFiles={1}
                    acceptedFileTypes={['image/jpeg', 'image/png', 'image/webp']}
                    initialFiles={selectedImages}
                  />
                </View>

                <View style={styles.scanInfo}>
                  <Icon name="info" size={20} color={Colors.primary} />
                  <Text style={styles.scanInfoText}>
                    Upload an image of a recipe and we'll extract the text for you using OCR technology.
                  </Text>
                </View>
              </View>
            )}

            {activeTab === 'voice' && (
              <View style={styles.voiceContainer}>
                <View style={styles.inputGroup}>
                  <Text style={styles.label}>Recipe Book *</Text>
                  <View style={styles.pickerContainer}>
                    <Picker
                      selectedValue={selectedProjectId || ''}
                      onValueChange={(value) => setSelectedProjectId(Number(value) || null)}
                      style={styles.picker}
                    >
                      <Picker.Item label="Select a recipe book" value="" />
                      {projects?.map((project) => (
                        <Picker.Item key={project.id} label={project.name} value={project.id} />
                      ))}
                    </Picker>
                  </View>
                </View>

                <View style={styles.voiceRecordContainer}>
                  <Icon name="mic" size={48} color={Colors.primary} />
                  <Text style={styles.voiceTitle}>Voice Recording</Text>
                  <Text style={styles.voiceDescription}>
                    Tap the microphone to start recording your recipe. Speak clearly and include ingredients and instructions.
                  </Text>
                  <Button
                    onPress={() => Alert.alert('Voice Recording', 'Voice recording feature coming soon!')}
                    style={styles.voiceButton}
                  >
                    <Icon name="mic" size={20} color={Colors.primaryForeground} />
                    <Text style={styles.voiceButtonText}>Start Recording</Text>
                  </Button>
                </View>
              </View>
            )}
          </CardContent>
        </Card>
      )}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.background,
  },
  loadingText: {
    marginTop: Spacing.md,
    fontSize: 16,
    color: Colors.foreground,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: Spacing.lg,
    backgroundColor: Colors.card,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: Colors.foreground,
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.xs,
  },
  addButtonText: {
    color: Colors.primaryForeground,
    marginLeft: Spacing.xs,
  },
  centerContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: Spacing.xl,
  },
  emptyText: {
    fontSize: 16,
    color: Colors.mutedForeground,
    textAlign: 'center',
    marginBottom: Spacing.lg,
  },
  createBookButton: {
    marginTop: Spacing.md,
  },
  formCard: {
    margin: Spacing.lg,
    marginBottom: Spacing.md,
  },
  formTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.cardForeground,
  },
  tabContainer: {
    flexDirection: 'row',
    marginBottom: Spacing.lg,
    borderRadius: BorderRadius.md,
    backgroundColor: Colors.muted,
    padding: 4,
  },
  tab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: Spacing.sm,
    paddingHorizontal: Spacing.xs,
    borderRadius: BorderRadius.sm,
    gap: Spacing.xs,
  },
  activeTab: {
    backgroundColor: Colors.primary,
  },
  tabText: {
    fontSize: 12,
    color: Colors.foreground,
    textAlign: 'center',
  },
  activeTabText: {
    color: Colors.primaryForeground,
    fontWeight: '600',
  },
  inputGroup: {
    marginBottom: Spacing.lg,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.foreground,
    marginBottom: Spacing.sm,
  },
  textArea: {
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: BorderRadius.md,
    padding: Spacing.md,
    fontSize: 16,
    backgroundColor: Colors.card,
    color: Colors.foreground,
    minHeight: 80,
    textAlignVertical: 'top',
  },
  pickerContainer: {
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: BorderRadius.md,
    backgroundColor: Colors.card,
  },
  picker: {
    color: Colors.foreground,
  },
  pickerSmall: {
    color: Colors.foreground,
    fontSize: 14,
  },
  measurementContainer: {
    flexDirection: 'row',
    gap: Spacing.sm,
  },
  measurementButton: {
    flex: 1,
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.sm,
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: BorderRadius.md,
    backgroundColor: Colors.card,
    alignItems: 'center',
  },
  measurementButtonActive: {
    backgroundColor: Colors.primary,
    borderColor: Colors.primary,
  },
  measurementButtonText: {
    fontSize: 14,
    color: Colors.foreground,
    textAlign: 'center',
  },
  measurementButtonTextActive: {
    color: Colors.primaryForeground,
    fontWeight: '600',
  },
  section: {
    marginBottom: Spacing.xl,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.foreground,
  },
  addButtonSmallText: {
    color: Colors.foreground,
    marginLeft: Spacing.xs,
  },
  ingredientRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.sm,
    marginBottom: Spacing.md,
  },
  ingredientName: {
    flex: 2,
  },
  ingredientAmount: {
    flex: 1,
  },
  ingredientUnit: {
    flex: 1,
  },
  removeButton: {
    padding: Spacing.sm,
    justifyContent: 'center',
    alignItems: 'center',
  },
  instructionRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: Spacing.sm,
    marginBottom: Spacing.md,
  },
  stepNumber: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: Colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: Spacing.sm,
  },
  stepNumberText: {
    color: Colors.primaryForeground,
    fontWeight: 'bold',
    fontSize: 14,
  },
  instructionInput: {
    flex: 1,
  },
  instructionTextArea: {
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: BorderRadius.md,
    padding: Spacing.md,
    fontSize: 16,
    backgroundColor: Colors.card,
    color: Colors.foreground,
    minHeight: 60,
    textAlignVertical: 'top',
  },
  formActions: {
    gap: Spacing.md,
    marginTop: Spacing.lg,
  },
  submitButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.xs,
  },
  submitButtonText: {
    color: Colors.primaryForeground,
    marginLeft: Spacing.xs,
  },
  cancelButton: {
    marginBottom: 0,
  },
  scanContainer: {
    alignItems: 'center',
  },
  scanInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.sm,
    padding: Spacing.md,
    backgroundColor: Colors.muted,
    borderRadius: BorderRadius.md,
    marginTop: Spacing.md,
  },
  scanInfoText: {
    flex: 1,
    fontSize: 14,
    color: Colors.foreground,
  },
  voiceContainer: {
    alignItems: 'center',
  },
  voiceRecordContainer: {
    alignItems: 'center',
    padding: Spacing.xl,
    backgroundColor: Colors.muted,
    borderRadius: BorderRadius.lg,
    marginTop: Spacing.lg,
  },
  voiceTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.foreground,
    marginTop: Spacing.md,
    marginBottom: Spacing.sm,
  },
  voiceDescription: {
    fontSize: 14,
    color: Colors.mutedForeground,
    textAlign: 'center',
    marginBottom: Spacing.lg,
  },
  voiceButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.sm,
  },
  voiceButtonText: {
    color: Colors.primaryForeground,
    marginLeft: Spacing.xs,
  },
});