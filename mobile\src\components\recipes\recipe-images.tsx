import React, { useState, useEffect } from 'react';
import { View, Image, Text, StyleSheet, ActivityIndicator } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Colors, API_URL } from '../../lib/constants';

interface RecipeImagesProps {
  images: string[];
  recipeTitle?: string;
  style?: any;
}

export function RecipeImages({ images, recipeTitle, style }: RecipeImagesProps) {
  const [presignedUrls, setPresignedUrls] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPresignedUrls = async () => {
      if (!images || images.length === 0) {
        setLoading(false);
        return;
      }

      try {
        const token = await AsyncStorage.getItem('token');
        if (!token) {
          throw new Error('Not authenticated');
        }

        // Prepare image keys with recipes/ prefix if needed (exactly like web)
        const imageKeys = images.map(image =>
          image.startsWith('recipes/') ? image : `recipes/${image}`
        );

        const response = await fetch(`${API_URL}/upload/presigned-urls`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
          },
          body: JSON.stringify({ keys: imageKeys }),
        });

        if (!response.ok) {
          throw new Error('Failed to fetch presigned URLs');
        }

        const data = await response.json();

        // Create a map of image key to presigned URL (exactly like web)
        const urlMap: Record<string, string> = {};
        data.presignedUrls.forEach((item: { key: string; url: string }) => {
          // Store both with and without recipes/ prefix for easier lookup
          urlMap[item.key] = item.url;
          if (item.key.startsWith('recipes/')) {
            urlMap[item.key.replace('recipes/', '')] = item.url;
          }
        });

        setPresignedUrls(urlMap);
      } catch (err) {
        console.error('Error fetching presigned URLs:', err);
        setError(err instanceof Error ? err.message : 'Failed to load images');
      } finally {
        setLoading(false);
      }
    };

    fetchPresignedUrls();
  }, [images]);

  if (loading) {
    return (
      <View style={[styles.loadingContainer, style]}>
        <ActivityIndicator size="large" color={Colors.primary} />
        <Text style={styles.loadingText}>Loading images...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={[styles.errorContainer, style]}>
        <Text style={styles.errorText}>Failed to load images</Text>
      </View>
    );
  }

  if (!images || images.length === 0) {
    return (
      <View style={[styles.placeholderContainer, style]}>
        <Text style={styles.placeholderIcon}>🍽️</Text>
        <Text style={styles.placeholderText}>No images available</Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, style]}>
      {images.map((image, index) => {
        // Try to find the URL with or without the recipes/ prefix
        const imageUrl = presignedUrls[image] ||
                         presignedUrls[`recipes/${image}`] ||
                         null;

        if (!imageUrl) {
          console.warn(`No presigned URL found for image: ${image}`);
          return (
            <View key={index} style={[styles.imagePlaceholder, style]}>
              <Text style={styles.placeholderIcon}>🍽️</Text>
              <Text style={styles.placeholderText}>Image unavailable</Text>
            </View>
          );
        }

        return (
          <Image
            key={index}
            style={[styles.image, style]}
            source={{ uri: imageUrl }}
            onError={() => console.warn(`Failed to load image: ${imageUrl}`)}
          />
        );
      })}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  image: {
    width: '100%',
    height: 200,
    borderRadius: 8,
    backgroundColor: Colors.muted,
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
    backgroundColor: Colors.muted,
    borderRadius: 8,
  },
  loadingText: {
    marginTop: 8,
    fontSize: 14,
    color: Colors.mutedForeground,
  },
  errorContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
    backgroundColor: Colors.muted,
    borderRadius: 8,
  },
  errorText: {
    fontSize: 14,
    color: Colors.destructive,
    textAlign: 'center',
  },
  placeholderContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
    backgroundColor: Colors.muted,
    borderRadius: 8,
  },
  imagePlaceholder: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
    backgroundColor: Colors.muted,
    borderRadius: 8,
    width: '100%',
    height: 200,
  },
  placeholderIcon: {
    fontSize: 32,
    marginBottom: 8,
  },
  placeholderText: {
    fontSize: 12,
    color: Colors.mutedForeground,
    textAlign: 'center',
  },
});
