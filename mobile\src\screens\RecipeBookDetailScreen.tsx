import React, { useEffect, useState, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  TextInput,
  Modal
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useLocation } from '../lib/router';
import { useToast } from '../hooks/use-toast';
import { useAuth } from '../hooks/use-auth';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Colors, Spacing, BorderRadius, API_URL } from '../lib/constants';
import { RecipeComments } from '../components/recipes/recipe-comments';
import { RecipeImages } from '../components/recipes/recipe-images';
import Icon from 'react-native-vector-icons/MaterialIcons';

interface Contributor {
  id: number;
  name: string;
  email: string;
  status: string;
}

interface Recipe {
  id: number;
  title: string;
  description: string;
  images: string[];
  role: string;
  tags: string[];
  ingredients: { name: string; amount: number; unit: string }[];
  instructions: string[];
  createdAt: string;
  status: string;
  contributor: {
    id: number;
    name: string;
  };
  project: {
    id: number;
    organizerId: number;
  };
}

interface Project {
  organizer: any;
  id: number;
  name: string;
  description: string;
  status: string;
  role: string;
  createdAt: string;
  contributors: Contributor[];
}

interface BookCustomizationOptions {
  theme: string;
  font: string;
  chapterStyle: string;
  cover: string;
  coverTitle: string;
  coverSubtitle: string;
  coverImage: string;
  useCustomCoverImage: boolean;
  dedication: string;
  familyQuotes: string[];
  includeDedication: boolean;
  includeQuotes: boolean;
}

export default function RecipeBookDetailScreen() {
  const [location, setLocation] = useLocation();
  const [project, setProject] = useState<Project | null>(null);
  const [recipes, setRecipes] = useState<Recipe[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [recipeToDelete, setRecipeToDelete] = useState<Recipe | null>(null);
  const [expandedRecipes, setExpandedRecipes] = useState<Record<number, { ingredients: boolean, instructions: boolean }>>({});
  const [searchQuery, setSearchQuery] = useState('');
  const [previewOpen, setPreviewOpen] = useState(false);
  const [bookCustomization, setBookCustomization] = useState<BookCustomizationOptions>({
    theme: 'classic',
    font: 'elegant',
    chapterStyle: 'simple',
    cover: 'classic',
    coverTitle: 'Family Cookbook',
    coverSubtitle: 'Treasured Recipes',
    coverImage: '',
    useCustomCoverImage: false,
    dedication: 'To my family, who have always supported my culinary adventures.',
    familyQuotes: [
      'Cooking is like love. It should be entered into with abandon or not at all. - Harriet Van Horne',
      'The secret ingredient is always love. - Grandma'
    ],
    includeDedication: true,
    includeQuotes: true
  });
  const [activeTab, setActiveTab] = useState<Record<number, string>>({});
  const { toast } = useToast();
  const { user } = useAuth();

  // Extract project ID from location
  const projectId = location.split('/').pop();

  useEffect(() => {
    if (!user) {
      setLocation("/login");
      return;
    }

    const fetchProjectDetails = async () => {
      try {
        const token = await AsyncStorage.getItem("token");
        if (!token) {
          throw new Error("No authentication token found");
        }

        let endpoint = '';
        // Use different endpoints based on user role
        if (user.role === 'organizer') {
          endpoint = `${API_URL}/organizer/all-projects`;
        } else if (user.role === 'contributor') {
          endpoint = `${API_URL}/contributor/projects/${projectId}`;
        } else if (user.role === 'admin') {
          endpoint = `${API_URL}/admin/projects/${projectId}`;
        } else {
          throw new Error("User role not supported");
        }

        const response = await fetch(endpoint, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        if (!response.ok) {
          const contentType = response.headers.get("content-type");
          if (contentType && contentType.includes("application/json")) {
            const errorData = await response.json();
            console.error('Error response:', errorData);
            throw new Error(errorData.message || "Failed to fetch project details");
          } else {
            const text = await response.text();
            console.error('Non-JSON error response:', text);
            throw new Error(`Server error: ${response.status} ${response.statusText}`);
          }
        }

        const data = await response.json();

        // Handle different response formats based on endpoint
        let foundProject;
        if (user.role === 'organizer') {
          // This endpoint returns a list of projects
          const projectIdNum = projectId ? parseInt(projectId) : 0;
          foundProject = data.projects.find((p: Project) => p.id === projectIdNum);
        } else if (user.role === 'contributor' || user.role === 'admin') {
          // These endpoints return a single project
          foundProject = data.project;
        }

        if (foundProject) {
          setProject(foundProject);
        } else {
          throw new Error("Project not found");
        }
      } catch (error) {
        console.error("Error fetching project details:", error);
        toast({
          title: "Error",
          description: error instanceof Error ? error.message : "Failed to load project details. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    const fetchRecipes = async () => {
      try {
        console.log('Fetching recipes for project:', projectId);
        const token = await AsyncStorage.getItem("token");
        if (!token) {
          throw new Error("No authentication token found");
        }

        let endpoint = '';
        // Use different endpoints based on user role
        if (user.role === 'organizer') {
          endpoint = `${API_URL}/organizer/projects/${projectId}/recipes`;
        } else if (user.role === 'contributor') {
          endpoint = `${API_URL}/contributor/projects/${projectId}/recipes`;
        } else if (user.role === 'admin') {
          endpoint = `${API_URL}/admin/projects/${projectId}/recipes`;
        } else {
          throw new Error("User role not supported");
        }

        console.log('Making request to:', endpoint);
        const response = await fetch(endpoint, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        console.log('Response status:', response.status);

        if (!response.ok) {
          const contentType = response.headers.get("content-type");
          if (contentType && contentType.includes("application/json")) {
            const errorData = await response.json();
            console.error('Error response:', errorData);
            throw new Error(errorData.message || "Failed to fetch recipes");
          } else {
            const text = await response.text();
            console.error('Non-JSON error response:', text);
            throw new Error(`Server error: ${response.status} ${response.statusText}`);
          }
        }

        const data = await response.json();
        console.log('Received recipes data:', data);
        if (!data.recipes) {
          console.error('No recipes array in response:', data);
          throw new Error('Invalid response format: missing recipes array');
        }

        // Filter recipes to only show approved ones
        const approvedRecipes = data.recipes.filter((recipe: Recipe) => {
          console.log(`Recipe ${recipe.id} status:`, recipe.status);
          return recipe.status === 'approved';
        });
        console.log('Filtered approved recipes:', approvedRecipes);
        setRecipes(approvedRecipes);
      } catch (error) {
        console.error("Error fetching recipes:", error);
        toast({
          title: "Error",
          description: error instanceof Error ? error.message : "Failed to load recipes. Please try again.",
          variant: "destructive",
        });
      }
    };

    fetchProjectDetails();
    fetchRecipes();
  }, [toast, user, projectId]);

  const handleEditRecipe = (recipe: Recipe) => {
    setLocation(`/recipes/${recipe.id}/edit`);
  };

  const handleDeleteRecipe = async (recipe: Recipe) => {
    try {
      const token = await AsyncStorage.getItem("token");
      const response = await fetch(`${API_URL}/organizer/recipes/${recipe.id}`, {
        method: 'DELETE',
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        const contentType = response.headers.get("content-type");
        if (contentType && contentType.includes("application/json")) {
          const errorData = await response.json();
          throw new Error(errorData.message || "Failed to delete recipe");
        } else {
          const text = await response.text();
          console.error('Non-JSON error response:', text);
          throw new Error(`Server error: ${response.status} ${response.statusText}`);
        }
      }

      // Only update the UI if the deletion was successful
      setRecipes(recipes.filter(r => r.id !== recipe.id));
      toast({
        title: "Success",
        description: "Recipe deleted successfully",
      });
    } catch (error) {
      console.error('Error deleting recipe:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to delete recipe. Please try again.",
        variant: "destructive",
      });
    }
  };

  const canEditRecipe = (recipe: Recipe) => {
    if (!project || !user) return false;
    // Check if user is creator, organizer, or admin
    return (
      recipe.contributor.id === user.id ||
      project.organizer.id === user.id ||
      user.role === 'admin'
    );
  };

  const toggleRecipeSection = (recipeId: number, section: 'ingredients' | 'instructions') => {
    setExpandedRecipes(prev => ({
      ...prev,
      [recipeId]: {
        ...prev[recipeId],
        [section]: !prev[recipeId]?.[section]
      }
    }));
  };

  const setRecipeTab = (recipeId: number, tab: string) => {
    setActiveTab(prev => ({ ...prev, [recipeId]: tab }));
  };

  // Add filtering logic
  const filteredRecipes = useMemo(() => {
    if (!searchQuery) return recipes;

    const searchLower = searchQuery.toLowerCase();
    return recipes.filter(recipe =>
      recipe.title.toLowerCase().includes(searchLower) ||
      recipe.description.toLowerCase().includes(searchLower) ||
      recipe.tags.some(tag => tag.toLowerCase().includes(searchLower)) ||
      recipe.contributor.name.toLowerCase().includes(searchLower)
    );
  }, [recipes, searchQuery]);

  const handlePreviewBook = () => {
    setPreviewOpen(true);
  };

  const handleCustomizeBook = () => {
    setPreviewOpen(true);
  };

  const handleBookCustomizationChange = (options: BookCustomizationOptions) => {
    setBookCustomization(options);
    // Save to AsyncStorage for persistence
    AsyncStorage.setItem('bookCustomization', JSON.stringify(options));
  };

  const generatePDF = async () => {
    try {
      toast({
        title: "Generating PDF",
        description: "Creating your recipe book PDF...",
      });

      const token = await AsyncStorage.getItem('token');
      const response = await fetch(`${API_URL}/pdf/generate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          recipes: filteredRecipes,
          options: bookCustomization,
          metadata: {
            title: project?.name || 'Family Recipe Collection',
            author: 'Family',
            description: 'A treasured collection of family recipes',
            createdAt: new Date().toISOString()
          }
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to generate PDF');
      }

      const blob = await response.blob();

      // For mobile, we would typically save to device storage or share
      toast({
        title: "PDF Generated",
        description: "Your recipe book PDF has been created successfully!",
      });

    } catch (error) {
      console.error('Error generating PDF:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to generate PDF",
        variant: "destructive",
      });
    }
  };

  if (!user) {
    return null;
  }

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={Colors.primary} />
        <Text style={styles.loadingText}>Loading...</Text>
      </View>
    );
  }

  if (!project) {
    return (
      <View style={styles.container}>
        <Card style={styles.errorCard}>
          <CardContent>
            <Text style={styles.errorText}>Project not found.</Text>
          </CardContent>
        </Card>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>{project.name}</Text>
        <View style={styles.headerActions}>
          <Button
            variant="outline"
            onPress={() => setLocation("/recipe-books")}
            size="sm"
          >
            Back to Recipe Books
          </Button>
        </View>
      </View>

      {/* Book Actions */}
      <View style={styles.bookActions}>
        <Button
          onPress={handlePreviewBook}
          style={styles.actionButton}
        >
          <Icon name="visibility" size={16} color={Colors.primaryForeground} />
          <Text style={styles.actionButtonText}>Preview Book</Text>
        </Button>
        <Button
          variant="outline"
          onPress={handleCustomizeBook}
          style={styles.actionButton}
        >
          <Icon name="palette" size={16} color={Colors.foreground} />
          <Text style={styles.actionButtonText}>Customize</Text>
        </Button>
      </View>

      {/* Search */}
      <View style={styles.searchContainer}>
        <View style={styles.searchInputContainer}>
          <Icon name="search" size={20} color={Colors.mutedForeground} style={styles.searchIcon} />
          <Input
            placeholder="Search recipes by name, description, tags, or contributor..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            style={styles.searchInput}
          />
        </View>
      </View>

      {/* Project Details Card */}
      <Card style={styles.card}>
        <CardHeader>
          <Text style={styles.cardTitle}>Project Details</Text>
          <Text style={styles.cardDescription}>
            Created on {new Date(project.createdAt).toLocaleDateString()}
            {project.organizer?.name && (
              <Text style={styles.organizerText}> by {project.organizer.name}</Text>
            )}
          </Text>
        </CardHeader>
        <CardContent>
          <Text style={styles.projectDescription}>{project.description}</Text>
          <View style={styles.projectMeta}>
            <View style={styles.statusContainer}>
              <Text style={styles.metaLabel}>Status:</Text>
              <View style={[
                styles.statusBadge,
                project.status === 'active' ? styles.statusActive :
                project.status === 'pending' ? styles.statusPending :
                styles.statusInactive
              ]}>
                <Text style={[
                  styles.statusText,
                  project.status === 'active' ? styles.statusActiveText :
                  project.status === 'pending' ? styles.statusPendingText :
                  styles.statusInactiveText
                ]}>{project.status}</Text>
              </View>
            </View>
            <View style={styles.roleContainer}>
              <Text style={styles.metaLabel}>Role:</Text>
              <View style={[
                styles.roleBadge,
                project.role === 'admin' ? styles.roleAdmin :
                project.role === 'organizer' ? styles.roleOrganizer :
                styles.roleContributor
              ]}>
                <Icon
                  name={
                    project.role === 'admin' ? 'star' :
                    project.role === 'organizer' ? 'shield' :
                    'person'
                  }
                  size={12}
                  color={
                    project.role === 'admin' ? '#7c3aed' :
                    project.role === 'organizer' ? '#2563eb' :
                    '#6b7280'
                  }
                />
                <Text style={[
                  styles.roleText,
                  project.role === 'admin' ? styles.roleAdminText :
                  project.role === 'organizer' ? styles.roleOrganizerText :
                  styles.roleContributorText
                ]}>{project.role}</Text>
              </View>
            </View>
          </View>
        </CardContent>
      </Card>

      {/* Contributors Card */}
      <Card style={styles.card}>
        <CardHeader>
          <Text style={styles.cardTitle}>Contributors</Text>
          <Text style={styles.cardDescription}>
            {project?.contributors?.length || 0} contributors
          </Text>
        </CardHeader>
        <CardContent>
          <View style={styles.contributorsList}>
            {project?.contributors?.map((contributor) => (
              <View key={contributor.id} style={styles.contributorItem}>
                <View style={styles.contributorInfo}>
                  <Text style={styles.contributorName}>{contributor.name}</Text>
                  <Text style={styles.contributorEmail}>{contributor.email}</Text>
                </View>
              </View>
            ))}
          </View>
        </CardContent>
      </Card>

      {/* Recipes Section */}
      <Card style={styles.card}>
        <CardHeader>
          <Text style={styles.cardTitle}>Recipes</Text>
          <Text style={styles.cardDescription}>
            {filteredRecipes.length} recipe{filteredRecipes.length !== 1 ? 's' : ''}
            {searchQuery && ` matching "${searchQuery}"`}
          </Text>
        </CardHeader>
        <CardContent>
          {filteredRecipes.length === 0 ? (
            <View style={styles.emptyRecipes}>
              <Text style={styles.emptyRecipesText}>
                {searchQuery ? 'No recipes match your search.' : 'No recipes in this book yet.'}
              </Text>
            </View>
          ) : (
            <View style={styles.recipesList}>
              {filteredRecipes.map((recipe) => (
                <Card key={recipe.id} style={styles.recipeCard}>
                  <CardContent>
                    <View style={styles.recipeHeader}>
                      <Text style={styles.recipeTitle}>{recipe.title}</Text>
                      <View style={styles.recipeActions}>
                        {canEditRecipe(recipe) && (
                          <>
                            <TouchableOpacity
                              onPress={() => handleEditRecipe(recipe)}
                              style={styles.actionButton}
                            >
                              <Icon name="edit" size={20} color={Colors.primary} />
                            </TouchableOpacity>
                            <TouchableOpacity
                              onPress={() => {
                                setRecipeToDelete(recipe);
                                setDeleteDialogOpen(true);
                              }}
                              style={styles.actionButton}
                            >
                              <Icon name="delete" size={20} color={Colors.destructive} />
                            </TouchableOpacity>
                          </>
                        )}
                      </View>
                    </View>

                    <Text style={styles.recipeDescription}>{recipe.description}</Text>

                    <View style={styles.recipeMeta}>
                      <Text style={styles.recipeContributor}>
                        By {recipe.contributor.name}
                      </Text>
                      <Text style={styles.recipeDate}>
                        {new Date(recipe.createdAt).toLocaleDateString()}
                      </Text>
                    </View>

                    {recipe.tags && recipe.tags.length > 0 && (
                      <View style={styles.recipeTags}>
                        {recipe.tags.map((tag, index) => (
                          <View key={index} style={styles.recipeTag}>
                            <Text style={styles.recipeTagText}>{tag}</Text>
                          </View>
                        ))}
                      </View>
                    )}

                    {/* Recipe Images */}
                    {recipe.images && recipe.images.length > 0 && (
                      <RecipeImages
                        images={recipe.images}
                        recipeTitle={recipe.title}
                      />
                    )}

                    {/* Recipe Tabs */}
                    <View style={styles.recipeTabs}>
                      <TouchableOpacity
                        style={[
                          styles.recipeTab,
                          (activeTab[recipe.id] || 'ingredients') === 'ingredients' && styles.recipeTabActive
                        ]}
                        onPress={() => setRecipeTab(recipe.id, 'ingredients')}
                      >
                        <Text style={[
                          styles.recipeTabText,
                          (activeTab[recipe.id] || 'ingredients') === 'ingredients' && styles.recipeTabTextActive
                        ]}>
                          Ingredients
                        </Text>
                      </TouchableOpacity>
                      <TouchableOpacity
                        style={[
                          styles.recipeTab,
                          activeTab[recipe.id] === 'instructions' && styles.recipeTabActive
                        ]}
                        onPress={() => setRecipeTab(recipe.id, 'instructions')}
                      >
                        <Text style={[
                          styles.recipeTabText,
                          activeTab[recipe.id] === 'instructions' && styles.recipeTabTextActive
                        ]}>
                          Instructions
                        </Text>
                      </TouchableOpacity>
                      <TouchableOpacity
                        style={[
                          styles.recipeTab,
                          activeTab[recipe.id] === 'comments' && styles.recipeTabActive
                        ]}
                        onPress={() => setRecipeTab(recipe.id, 'comments')}
                      >
                        <Text style={[
                          styles.recipeTabText,
                          activeTab[recipe.id] === 'comments' && styles.recipeTabTextActive
                        ]}>
                          Comments
                        </Text>
                      </TouchableOpacity>
                    </View>

                    {/* Recipe Content */}
                    <View style={styles.recipeContent}>
                      {(activeTab[recipe.id] || 'ingredients') === 'ingredients' && (
                        <View style={styles.ingredientsSection}>
                          <View style={styles.ingredientsHeader}>
                            <Text style={styles.sectionTitle}>Ingredients</Text>
                            <TouchableOpacity
                              onPress={() => toggleRecipeSection(recipe.id, 'ingredients')}
                              style={styles.toggleButton}
                            >
                              <Icon
                                name={expandedRecipes[recipe.id]?.ingredients ? 'expand-less' : 'expand-more'}
                                size={20}
                                color={Colors.foreground}
                              />
                            </TouchableOpacity>
                          </View>
                          {(expandedRecipes[recipe.id]?.ingredients || recipe.ingredients.length <= 3) && (
                            <View style={styles.ingredientsList}>
                              {recipe.ingredients.map((ingredient, index) => (
                                <View key={index} style={styles.ingredientItem}>
                                  <Text style={styles.ingredientText}>
                                    {ingredient.amount} {ingredient.unit} {ingredient.name}
                                  </Text>
                                </View>
                              ))}
                            </View>
                          )}
                        </View>
                      )}

                      {activeTab[recipe.id] === 'instructions' && (
                        <View style={styles.instructionsSection}>
                          <View style={styles.instructionsHeader}>
                            <Text style={styles.sectionTitle}>Instructions</Text>
                            <TouchableOpacity
                              onPress={() => toggleRecipeSection(recipe.id, 'instructions')}
                              style={styles.toggleButton}
                            >
                              <Icon
                                name={expandedRecipes[recipe.id]?.instructions ? 'expand-less' : 'expand-more'}
                                size={20}
                                color={Colors.foreground}
                              />
                            </TouchableOpacity>
                          </View>
                          {(expandedRecipes[recipe.id]?.instructions || recipe.instructions.length <= 3) && (
                            <View style={styles.instructionsList}>
                              {recipe.instructions.map((instruction, index) => (
                                <View key={index} style={styles.instructionItem}>
                                  <View style={styles.instructionNumber}>
                                    <Text style={styles.instructionNumberText}>{index + 1}</Text>
                                  </View>
                                  <Text style={styles.instructionText}>{instruction}</Text>
                                </View>
                              ))}
                            </View>
                          )}
                        </View>
                      )}

                      {activeTab[recipe.id] === 'comments' && (
                        <RecipeComments
                          recipeId={recipe.id}
                          projectId={project.id}
                        />
                      )}
                    </View>
                  </CardContent>
                </Card>
              ))}
            </View>
          )}
        </CardContent>
      </Card>

      {/* Book Preview Modal */}
      {previewOpen && (
        <Modal
          visible={previewOpen}
          animationType="slide"
          presentationStyle="fullScreen"
          onRequestClose={() => setPreviewOpen(false)}
        >
          <View style={styles.previewModal}>
            <View style={styles.previewHeader}>
              <Text style={styles.previewTitle}>Book Preview</Text>
              <TouchableOpacity
                onPress={() => setPreviewOpen(false)}
                style={styles.closeButton}
              >
                <Icon name="close" size={24} color={Colors.foreground} />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.previewContent}>
              <Text style={styles.previewDescription}>
                This is a preview of how your recipe book will look when printed.
                You can customize the theme, fonts, and layout using the options below.
              </Text>

              <View style={styles.previewActions}>
                <Button
                  onPress={generatePDF}
                  style={styles.previewActionButton}
                >
                  <Icon name="picture-as-pdf" size={16} color={Colors.primaryForeground} />
                  <Text style={styles.previewActionText}>Generate PDF</Text>
                </Button>
                <Button
                  variant="outline"
                  onPress={() => {
                    Alert.alert(
                      'Customization',
                      'Book customization options would be available here, including themes, fonts, covers, and layout options.'
                    );
                  }}
                  style={styles.previewActionButton}
                >
                  <Icon name="palette" size={16} color={Colors.foreground} />
                  <Text style={styles.previewActionText}>Customize</Text>
                </Button>
              </View>
            </ScrollView>
          </View>
        </Modal>
      )}

      {/* Delete Confirmation Dialog */}
      {deleteDialogOpen && recipeToDelete && (
        <Modal
          visible={deleteDialogOpen}
          transparent
          animationType="fade"
          onRequestClose={() => setDeleteDialogOpen(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.deleteDialog}>
              <Text style={styles.deleteDialogTitle}>Delete Recipe</Text>
              <Text style={styles.deleteDialogText}>
                Are you sure you want to delete "{recipeToDelete.title}"? This action cannot be undone.
              </Text>
              <View style={styles.deleteDialogActions}>
                <Button
                  variant="outline"
                  onPress={() => {
                    setDeleteDialogOpen(false);
                    setRecipeToDelete(null);
                  }}
                  style={styles.deleteDialogButton}
                >
                  Cancel
                </Button>
                <Button
                  onPress={() => {
                    if (recipeToDelete) {
                      handleDeleteRecipe(recipeToDelete);
                    }
                    setDeleteDialogOpen(false);
                    setRecipeToDelete(null);
                  }}
                  style={[styles.deleteDialogButton, styles.deleteDialogButtonDestructive]}
                >
                  Delete
                </Button>
              </View>
            </View>
          </View>
        </Modal>
      )}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.background,
  },
  loadingText: {
    marginTop: Spacing.md,
    fontSize: 16,
    color: Colors.foreground,
  },
  errorCard: {
    margin: Spacing.lg,
  },
  errorText: {
    fontSize: 16,
    color: Colors.destructive,
    textAlign: 'center',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: Spacing.lg,
    backgroundColor: Colors.card,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: Colors.foreground,
    flex: 1,
  },
  headerActions: {
    marginLeft: Spacing.md,
  },
  bookActions: {
    flexDirection: 'row',
    gap: Spacing.md,
    padding: Spacing.lg,
    backgroundColor: Colors.card,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: Spacing.sm,
  },
  actionButtonText: {
    marginLeft: Spacing.xs,
  },
  searchContainer: {
    padding: Spacing.lg,
    backgroundColor: Colors.card,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.background,
    borderRadius: BorderRadius.md,
    borderWidth: 1,
    borderColor: Colors.border,
    paddingHorizontal: Spacing.md,
  },
  searchIcon: {
    marginRight: Spacing.sm,
  },
  searchInput: {
    flex: 1,
    borderWidth: 0,
    backgroundColor: 'transparent',
  },
  card: {
    margin: Spacing.lg,
    marginBottom: Spacing.md,
  },
  cardTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.cardForeground,
  },
  cardDescription: {
    fontSize: 14,
    color: Colors.mutedForeground,
    marginTop: Spacing.xs,
  },
  organizerText: {
    fontWeight: '600',
    color: Colors.foreground,
  },
  projectDescription: {
    fontSize: 16,
    color: Colors.foreground,
    marginBottom: Spacing.lg,
    lineHeight: 24,
  },
  projectMeta: {
    flexDirection: 'row',
    gap: Spacing.lg,
    flexWrap: 'wrap',
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.sm,
  },
  roleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.sm,
  },
  metaLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.foreground,
  },
  statusBadge: {
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.sm,
    borderWidth: 1,
  },
  statusActive: {
    backgroundColor: '#dcfce7',
    borderColor: '#16a34a',
  },
  statusPending: {
    backgroundColor: '#fef3c7',
    borderColor: '#d97706',
  },
  statusInactive: {
    backgroundColor: '#fee2e2',
    borderColor: '#dc2626',
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
    textTransform: 'capitalize',
  },
  statusActiveText: {
    color: '#16a34a',
  },
  statusPendingText: {
    color: '#d97706',
  },
  statusInactiveText: {
    color: '#dc2626',
  },
  roleBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.xs,
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.sm,
    borderWidth: 1,
  },
  roleAdmin: {
    backgroundColor: '#f3e8ff',
    borderColor: '#7c3aed',
  },
  roleOrganizer: {
    backgroundColor: '#dbeafe',
    borderColor: '#2563eb',
  },
  roleContributor: {
    backgroundColor: '#f3f4f6',
    borderColor: '#6b7280',
  },
  roleText: {
    fontSize: 12,
    fontWeight: '600',
    textTransform: 'capitalize',
  },
  roleAdminText: {
    color: '#7c3aed',
  },
  roleOrganizerText: {
    color: '#2563eb',
  },
  roleContributorText: {
    color: '#6b7280',
  },
  contributorsList: {
    gap: Spacing.md,
  },
  contributorItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: Spacing.md,
    backgroundColor: Colors.muted,
    borderRadius: BorderRadius.md,
  },
  contributorInfo: {
    flex: 1,
  },
  contributorName: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.foreground,
    marginBottom: Spacing.xs,
  },
  contributorEmail: {
    fontSize: 14,
    color: Colors.mutedForeground,
  },
  emptyRecipes: {
    padding: Spacing.xl,
    alignItems: 'center',
  },
  emptyRecipesText: {
    fontSize: 16,
    color: Colors.mutedForeground,
    textAlign: 'center',
  },
  recipesList: {
    gap: Spacing.lg,
  },
  recipeCard: {
    marginBottom: Spacing.md,
  },
  recipeHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: Spacing.sm,
  },
  recipeTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.foreground,
    flex: 1,
    marginRight: Spacing.md,
  },
  recipeActions: {
    flexDirection: 'row',
    gap: Spacing.sm,
  },
  actionButton: {
    padding: Spacing.sm,
    borderRadius: BorderRadius.sm,
  },
  recipeDescription: {
    fontSize: 14,
    color: Colors.foreground,
    marginBottom: Spacing.md,
    lineHeight: 20,
  },
  recipeMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },
  recipeContributor: {
    fontSize: 12,
    color: Colors.mutedForeground,
    fontWeight: '600',
  },
  recipeDate: {
    fontSize: 12,
    color: Colors.mutedForeground,
  },
  recipeTags: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: Spacing.xs,
    marginBottom: Spacing.md,
  },
  recipeTag: {
    backgroundColor: Colors.muted,
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.sm,
  },
  recipeTagText: {
    fontSize: 12,
    color: Colors.foreground,
  },
  recipeTabs: {
    flexDirection: 'row',
    marginBottom: Spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  recipeTab: {
    flex: 1,
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.sm,
    alignItems: 'center',
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  recipeTabActive: {
    borderBottomColor: Colors.primary,
  },
  recipeTabText: {
    fontSize: 14,
    color: Colors.mutedForeground,
  },
  recipeTabTextActive: {
    color: Colors.primary,
    fontWeight: '600',
  },
  recipeContent: {
    marginTop: Spacing.md,
  },
  ingredientsSection: {
    marginBottom: Spacing.md,
  },
  ingredientsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  toggleButton: {
    padding: Spacing.xs,
  },
  ingredientsList: {
    gap: Spacing.sm,
  },
  ingredientItem: {
    paddingVertical: Spacing.xs,
    paddingHorizontal: Spacing.sm,
    backgroundColor: Colors.muted,
    borderRadius: BorderRadius.sm,
  },
  ingredientText: {
    fontSize: 14,
    color: Colors.foreground,
  },
  instructionsSection: {
    marginBottom: Spacing.md,
  },
  instructionsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  instructionsList: {
    gap: Spacing.md,
  },
  instructionItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: Spacing.sm,
  },
  instructionNumber: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: Colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 2,
  },
  instructionNumberText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: Colors.primaryForeground,
  },
  instructionText: {
    flex: 1,
    fontSize: 14,
    color: Colors.foreground,
    lineHeight: 20,
  },
  previewModal: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  previewHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: Spacing.lg,
    backgroundColor: Colors.card,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  previewTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.foreground,
  },
  closeButton: {
    padding: Spacing.sm,
  },
  previewContent: {
    flex: 1,
    padding: Spacing.lg,
  },
  previewDescription: {
    fontSize: 16,
    color: Colors.foreground,
    marginBottom: Spacing.xl,
    lineHeight: 24,
  },
  previewActions: {
    gap: Spacing.md,
  },
  previewActionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: Spacing.sm,
  },
  previewActionText: {
    marginLeft: Spacing.xs,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: Spacing.lg,
  },
  deleteDialog: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    padding: Spacing.xl,
    width: '100%',
    maxWidth: 400,
  },
  deleteDialogTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.foreground,
    marginBottom: Spacing.md,
  },
  deleteDialogText: {
    fontSize: 16,
    color: Colors.foreground,
    marginBottom: Spacing.xl,
    lineHeight: 24,
  },
  deleteDialogActions: {
    flexDirection: 'row',
    gap: Spacing.md,
  },
  deleteDialogButton: {
    flex: 1,
  },
  deleteDialogButtonDestructive: {
    backgroundColor: Colors.destructive,
  },
});
