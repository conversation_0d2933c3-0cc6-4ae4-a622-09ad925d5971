import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image, Alert, ScrollView } from 'react-native';
import { launchImageLibrary, ImagePickerResponse, MediaType } from 'react-native-image-picker';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Colors, Spacing, BorderRadius } from '../../lib/constants';
import { Button } from './button';
import Icon from 'react-native-vector-icons/Feather';

interface ImageUploadProps {
  onUpload: (files: string[]) => void;
  maxFiles?: number;
  acceptedFileTypes?: string[];
  initialFiles?: string[];
}

export function ImageUpload({
  onUpload,
  maxFiles = 3,
  acceptedFileTypes = ['image/jpeg', 'image/png', 'image/webp'],
  initialFiles = []
}: ImageUploadProps) {
  const [selectedImages, setSelectedImages] = useState<string[]>(initialFiles);
  const [uploading, setUploading] = useState(false);

  const handleImagePicker = () => {
    if (selectedImages.length >= maxFiles) {
      Alert.alert(
        'Maximum Files Reached',
        `You can only upload up to ${maxFiles} images.`,
        [{ text: 'OK' }]
      );
      return;
    }

    const options = {
      mediaType: 'photo' as MediaType,
      includeBase64: false,
      maxHeight: 2000,
      maxWidth: 2000,
      quality: 0.8,
      selectionLimit: maxFiles - selectedImages.length,
    };

    launchImageLibrary(options, (response: ImagePickerResponse) => {
      if (response.didCancel || response.errorMessage) {
        return;
      }

      if (response.assets) {
        uploadImages(response.assets);
      }
    });
  };

  const uploadImages = async (assets: any[]) => {
    setUploading(true);
    const uploadedFiles: string[] = [];

    try {
      for (const asset of assets) {
        if (!asset.uri) continue;

        // Create FormData for upload
        const formData = new FormData();
        formData.append('file', {
          uri: asset.uri,
          type: asset.type || 'image/jpeg',
          name: asset.fileName || `image_${Date.now()}.jpg`,
        } as any);

        // Upload to server
        const token = await AsyncStorage.getItem('token');
        if (!token) {
          throw new Error('Not authenticated');
        }

        const response = await fetch(`${process.env.EXPO_PUBLIC_API_URL || 'http://localhost:3000'}/api/images/upload`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'multipart/form-data',
          },
          body: formData,
        });

        if (!response.ok) {
          throw new Error('Failed to upload image');
        }

        const data = await response.json();
        uploadedFiles.push(data.key || data.filename);
      }

      const newImages = [...selectedImages, ...uploadedFiles];
      setSelectedImages(newImages);
      onUpload(newImages);
    } catch (error) {
      console.error('Error uploading images:', error);
      Alert.alert(
        'Upload Error',
        'Failed to upload one or more images. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setUploading(false);
    }
  };

  const removeImage = (index: number) => {
    const newImages = selectedImages.filter((_, i) => i !== index);
    setSelectedImages(newImages);
    onUpload(newImages);
  };

  return (
    <View style={styles.container}>
      <Text style={styles.label}>Recipe Images</Text>
      <Text style={styles.description}>
        Upload up to {maxFiles} images of your recipe
      </Text>

      {/* Upload Button */}
      <TouchableOpacity
        style={[styles.uploadArea, uploading && styles.uploadAreaDisabled]}
        onPress={handleImagePicker}
        disabled={uploading || selectedImages.length >= maxFiles}
      >
        <View style={styles.uploadContent}>
          <Icon
            name="camera"
            size={32}
            color={uploading ? Colors.mutedForeground : Colors.primary}
          />
          <Text style={[styles.uploadText, uploading && styles.uploadTextDisabled]}>
            {uploading ? 'Uploading...' : 'Tap to select images'}
          </Text>
          <Text style={styles.uploadSubtext}>
            {selectedImages.length}/{maxFiles} images selected
          </Text>
        </View>
      </TouchableOpacity>

      {/* Selected Images */}
      {selectedImages.length > 0 && (
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.imagesContainer}>
          {selectedImages.map((image, index) => (
            <View key={index} style={styles.imageWrapper}>
              <Image
                source={{ uri: image.startsWith('http') ? image : `${process.env.EXPO_PUBLIC_API_URL || 'http://localhost:3000'}/uploads/${image}` }}
                style={styles.previewImage}
                resizeMode="cover"
              />
              <TouchableOpacity
                style={styles.removeButton}
                onPress={() => removeImage(index)}
              >
                <Icon name="x" size={16} color={Colors.background} />
              </TouchableOpacity>
            </View>
          ))}
        </ScrollView>
      )}

      {/* File Type Info */}
      <Text style={styles.fileInfo}>
        Supported formats: JPEG, PNG, WebP • Max size: 10MB per image
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: Spacing.lg,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.foreground,
    marginBottom: Spacing.xs,
  },
  description: {
    fontSize: 14,
    color: Colors.mutedForeground,
    marginBottom: Spacing.md,
  },
  uploadArea: {
    borderWidth: 2,
    borderColor: Colors.border,
    borderStyle: 'dashed',
    borderRadius: BorderRadius.lg,
    backgroundColor: Colors.muted,
    padding: Spacing.xl,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 120,
  },
  uploadAreaDisabled: {
    opacity: 0.5,
  },
  uploadContent: {
    alignItems: 'center',
    gap: Spacing.sm,
  },
  uploadText: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.foreground,
  },
  uploadTextDisabled: {
    color: Colors.mutedForeground,
  },
  uploadSubtext: {
    fontSize: 12,
    color: Colors.mutedForeground,
  },
  imagesContainer: {
    marginTop: Spacing.md,
    marginBottom: Spacing.sm,
  },
  imageWrapper: {
    position: 'relative',
    marginRight: Spacing.md,
  },
  previewImage: {
    width: 80,
    height: 80,
    borderRadius: BorderRadius.md,
    backgroundColor: Colors.muted,
  },
  removeButton: {
    position: 'absolute',
    top: -8,
    right: -8,
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: Colors.destructive,
    justifyContent: 'center',
    alignItems: 'center',
  },
  fileInfo: {
    fontSize: 12,
    color: Colors.mutedForeground,
    textAlign: 'center',
    marginTop: Spacing.sm,
  },
});
