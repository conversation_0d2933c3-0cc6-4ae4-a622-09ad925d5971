import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TextInput } from 'react-native';
import { Card, CardContent, CardHeader } from '../ui/card';
import { Colors, Spacing, BorderRadius } from '../../lib/constants';
import { Picker } from '@react-native-picker/picker';
import Slider from '@react-native-community/slider';

// Constants matching web version exactly
const PricingTier = {
  SMALL: 'small',
  MEDIUM: 'medium',
  LARGE: 'large'
};

const PricingModel = {
  [PricingTier.SMALL]: {
    name: 'Small Cookbook',
    description: 'Perfect for family recipes',
    basePrice: 15.99,
    pricePerPage: 0.25,
    maxPages: 50
  },
  [PricingTier.MEDIUM]: {
    name: 'Medium Cookbook',
    description: 'Great for community collections',
    basePrice: 24.99,
    pricePerPage: 0.30,
    maxPages: 100
  },
  [PricingTier.LARGE]: {
    name: 'Large Cookbook',
    description: 'Professional cookbook publishing',
    basePrice: 39.99,
    pricePerPage: 0.35,
    maxPages: 200
  }
};

const PAGE_COUNT_PER_RECIPE = 2;
const MINIMUM_PAGE_COUNT = 20;

interface PricingCalculatorProps {
  initialTier?: string;
  recipeCount?: number;
  onPricingChange?: (pricing: {
    tier: string;
    basePrice: number;
    pagePrice: number;
    totalPrice: number;
    estimatedPages: number;
  }) => void;
  style?: any;
  compact?: boolean;
}

export function PricingCalculator({
  initialTier = PricingTier.SMALL,
  recipeCount = 0,
  onPricingChange,
  style,
  compact = false
}: PricingCalculatorProps) {
  const [tier, setTier] = useState(initialTier);
  const [customRecipeCount, setCustomRecipeCount] = useState(recipeCount);
  const [estimatedPages, setEstimatedPages] = useState(Math.max(MINIMUM_PAGE_COUNT, recipeCount * PAGE_COUNT_PER_RECIPE));

  // Update tier when initialTier prop changes
  useEffect(() => {
    setTier(initialTier);
  }, [initialTier]);

  // Calculate pricing whenever tier or estimated pages change
  useEffect(() => {
    // Use either the provided recipe count or the custom one
    const effectiveRecipeCount = recipeCount || customRecipeCount;

    // Calculate estimated pages based on recipe count
    const calculatedPages = Math.max(MINIMUM_PAGE_COUNT, effectiveRecipeCount * PAGE_COUNT_PER_RECIPE);
    setEstimatedPages(calculatedPages);

    // Calculate pricing
    const tierData = PricingModel[tier as keyof typeof PricingModel];
    const basePrice = tierData.basePrice;
    const pagePrice = tierData.pricePerPage * calculatedPages;
    const totalPrice = basePrice + pagePrice;

    // Notify parent component if callback provided
    if (onPricingChange) {
      onPricingChange({
        tier,
        basePrice,
        pagePrice,
        totalPrice,
        estimatedPages: calculatedPages
      });
    }
  }, [tier, recipeCount, customRecipeCount, onPricingChange]);

  const handleTierChange = (value: string) => {
    setTier(value);
  };

  const handleRecipeCountChange = (text: string) => {
    const count = parseInt(text) || 0;
    setCustomRecipeCount(count);
  };

  const handlePageSliderChange = (value: number) => {
    // If user manually adjusts the page count, update it
    // This overrides the automatic calculation
    setEstimatedPages(value);
  };

  // Get current tier data
  const tierData = PricingModel[tier as keyof typeof PricingModel];

  // Calculate pricing
  const basePrice = tierData.basePrice;
  const pagePrice = tierData.pricePerPage * estimatedPages;
  const totalPrice = basePrice + pagePrice;

  return (
    <Card style={[styles.card, style]}>
      <CardHeader style={styles.header}>
        <Text style={styles.title}>Pricing Calculator</Text>
        <Text style={styles.description}>
          Estimate the cost of your cookbook based on size and number of recipes
        </Text>
      </CardHeader>
      <CardContent style={styles.content}>
        <ScrollView showsVerticalScrollIndicator={false}>
          {/* Cookbook Size Selection */}
          <View style={styles.section}>
            <Text style={styles.label}>Cookbook Size</Text>
            <View style={styles.pickerContainer}>
              <Picker
                selectedValue={tier}
                onValueChange={handleTierChange}
                style={styles.picker}
              >
                <Picker.Item
                  label={`${PricingModel[PricingTier.SMALL].name} - ${PricingModel[PricingTier.SMALL].description}`}
                  value={PricingTier.SMALL}
                />
                <Picker.Item
                  label={`${PricingModel[PricingTier.MEDIUM].name} - ${PricingModel[PricingTier.MEDIUM].description}`}
                  value={PricingTier.MEDIUM}
                />
                <Picker.Item
                  label={`${PricingModel[PricingTier.LARGE].name} - ${PricingModel[PricingTier.LARGE].description}`}
                  value={PricingTier.LARGE}
                />
              </Picker>
            </View>
          </View>

          {/* Recipe Count Input (only show if no recipeCount prop provided) */}
          {!recipeCount && (
            <View style={styles.section}>
              <Text style={styles.label}>Number of Recipes</Text>
              <TextInput
                style={styles.textInput}
                value={customRecipeCount.toString()}
                onChangeText={handleRecipeCountChange}
                placeholder="Enter number of recipes"
                keyboardType="numeric"
                placeholderTextColor={Colors.mutedForeground}
              />
            </View>
          )}

          {/* Page Count Slider */}
          <View style={styles.section}>
            <Text style={styles.label}>Estimated Pages: {estimatedPages}</Text>
            <Slider
              style={styles.slider}
              minimumValue={MINIMUM_PAGE_COUNT}
              maximumValue={tierData.maxPages}
              value={estimatedPages}
              onValueChange={handlePageSliderChange}
              step={1}
              minimumTrackTintColor={Colors.primary}
              maximumTrackTintColor={Colors.muted}
              thumbStyle={{ backgroundColor: Colors.primary }}
            />
            <View style={styles.sliderLabels}>
              <Text style={styles.sliderLabel}>{MINIMUM_PAGE_COUNT}</Text>
              <Text style={styles.sliderLabel}>{tierData.maxPages}</Text>
            </View>
          </View>

          {/* Pricing Breakdown */}
          <View style={styles.pricingSection}>
            <Text style={styles.pricingTitle}>Pricing Breakdown</Text>

            <View style={styles.pricingRow}>
              <Text style={styles.pricingLabel}>Base Price ({tierData.name})</Text>
              <Text style={styles.pricingValue}>${basePrice.toFixed(2)}</Text>
            </View>

            <View style={styles.pricingRow}>
              <Text style={styles.pricingLabel}>Pages ({estimatedPages} × ${tierData.pricePerPage})</Text>
              <Text style={styles.pricingValue}>${pagePrice.toFixed(2)}</Text>
            </View>

            <View style={[styles.pricingRow, styles.totalRow]}>
              <Text style={styles.totalLabel}>Total Price</Text>
              <Text style={styles.totalValue}>${totalPrice.toFixed(2)}</Text>
            </View>
          </View>

          {/* Additional Info */}
          <View style={styles.infoSection}>
            <Text style={styles.infoText}>
              • Prices include professional printing and binding
            </Text>
            <Text style={styles.infoText}>
              • Free shipping on orders over $50
            </Text>
            <Text style={styles.infoText}>
              • Bulk discounts available for 10+ copies
            </Text>
          </View>
        </ScrollView>
      </CardContent>
    </Card>
  );
}

const styles = StyleSheet.create({
  card: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
  },
  header: {
    padding: Spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: Colors.foreground,
    marginBottom: Spacing.xs,
  },
  description: {
    fontSize: 14,
    color: Colors.mutedForeground,
    lineHeight: 20,
  },
  content: {
    padding: Spacing.lg,
  },
  section: {
    marginBottom: Spacing.lg,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.foreground,
    marginBottom: Spacing.sm,
  },
  pickerContainer: {
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: BorderRadius.md,
    backgroundColor: Colors.background,
  },
  picker: {
    height: 50,
    color: Colors.foreground,
  },
  textInput: {
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: BorderRadius.md,
    padding: Spacing.md,
    fontSize: 16,
    color: Colors.foreground,
    backgroundColor: Colors.background,
  },
  slider: {
    width: '100%',
    height: 40,
  },
  sliderLabels: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: Spacing.xs,
  },
  sliderLabel: {
    fontSize: 12,
    color: Colors.mutedForeground,
  },
  pricingSection: {
    backgroundColor: Colors.muted,
    padding: Spacing.md,
    borderRadius: BorderRadius.md,
    marginBottom: Spacing.lg,
  },
  pricingTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.foreground,
    marginBottom: Spacing.md,
  },
  pricingRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  pricingLabel: {
    fontSize: 14,
    color: Colors.foreground,
    flex: 1,
  },
  pricingValue: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.foreground,
  },
  totalRow: {
    borderTopWidth: 1,
    borderTopColor: Colors.border,
    paddingTop: Spacing.sm,
    marginTop: Spacing.sm,
    marginBottom: 0,
  },
  totalLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.foreground,
  },
  totalValue: {
    fontSize: 18,
    fontWeight: '700',
    color: Colors.primary,
  },
  infoSection: {
    marginTop: Spacing.md,
  },
  infoText: {
    fontSize: 12,
    color: Colors.mutedForeground,
    marginBottom: Spacing.xs,
    lineHeight: 16,
  },
});


