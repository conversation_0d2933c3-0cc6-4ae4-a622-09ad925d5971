import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, Alert } from 'react-native';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Textarea } from '../ui/textarea';
import { Label } from '../ui/label';
import { Select } from '../ui/select';
import { Card } from '../ui/card';
import { ImageUpload } from '../ui/image-upload';
import { useToast } from '../../hooks/use-toast';

interface Project {
  id: number;
  name: string;
  organizerId: number;
}

interface RecipeFormProps {
  projects: Project[];
  onSubmit: (data: any) => void;
  initialData?: any;
}

export function RecipeForm({ projects, onSubmit, initialData }: RecipeFormProps) {
  const [formData, setFormData] = useState({
    title: initialData?.title || '',
    description: initialData?.description || '',
    ingredients: initialData?.ingredients || [''],
    instructions: initialData?.instructions || [''],
    prepTime: initialData?.prepTime || '',
    cookTime: initialData?.cookTime || '',
    servings: initialData?.servings || '',
    difficulty: initialData?.difficulty || 'easy',
    tags: initialData?.tags || '',
    projectId: initialData?.projectId || '',
    images: initialData?.images || [],
  });

  const { toast } = useToast();

  const addIngredient = () => {
    if (formData.ingredients.length < 15) {
      setFormData(prev => ({
        ...prev,
        ingredients: [...prev.ingredients, '']
      }));
    } else {
      toast({
        title: 'Limit Reached',
        description: 'Maximum 15 ingredients allowed',
        variant: 'destructive'
      });
    }
  };

  const removeIngredient = (index: number) => {
    setFormData(prev => ({
      ...prev,
      ingredients: prev.ingredients.filter((_, i) => i !== index)
    }));
  };

  const updateIngredient = (index: number, value: string) => {
    setFormData(prev => ({
      ...prev,
      ingredients: prev.ingredients.map((item, i) => i === index ? value : item)
    }));
  };

  const addInstruction = () => {
    if (formData.instructions.length < 15) {
      setFormData(prev => ({
        ...prev,
        instructions: [...prev.instructions, '']
      }));
    } else {
      toast({
        title: 'Limit Reached',
        description: 'Maximum 15 instructions allowed',
        variant: 'destructive'
      });
    }
  };

  const removeInstruction = (index: number) => {
    setFormData(prev => ({
      ...prev,
      instructions: prev.instructions.filter((_, i) => i !== index)
    }));
  };

  const updateInstruction = (index: number, value: string) => {
    setFormData(prev => ({
      ...prev,
      instructions: prev.instructions.map((item, i) => i === index ? value : item)
    }));
  };

  const handleImageUpload = (images: string[]) => {
    setFormData(prev => ({
      ...prev,
      images
    }));
  };

  const handleSubmit = () => {
    if (!formData.title.trim()) {
      toast({
        title: 'Error',
        description: 'Recipe title is required',
        variant: 'destructive'
      });
      return;
    }

    if (!formData.projectId) {
      toast({
        title: 'Error',
        description: 'Please select a recipe book',
        variant: 'destructive'
      });
      return;
    }

    const cleanedData = {
      ...formData,
      ingredients: formData.ingredients.filter(item => item.trim()),
      instructions: formData.instructions.filter(item => item.trim()),
      tags: formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag),
    };

    onSubmit(cleanedData);
  };

  const projectOptions = projects.map(project => ({
    value: project.id.toString(),
    label: project.name
  }));

  const difficultyOptions = [
    { value: 'easy', label: 'Easy' },
    { value: 'medium', label: 'Medium' },
    { value: 'hard', label: 'Hard' },
  ];

  return (
    <ScrollView style={styles.container}>
      <Card style={styles.card}>
        <View style={styles.content}>
          <Text style={styles.title}>Recipe Details</Text>

          <View style={styles.field}>
            <Label>Recipe Title *</Label>
            <Input
              value={formData.title}
              onChangeText={(value) => setFormData(prev => ({ ...prev, title: value }))}
              placeholder="Enter recipe title"
            />
          </View>

          <View style={styles.field}>
            <Label>Description</Label>
            <Textarea
              value={formData.description}
              onChangeText={(value) => setFormData(prev => ({ ...prev, description: value }))}
              placeholder="Describe your recipe..."
            />
          </View>

          <View style={styles.field}>
            <Label>Recipe Book *</Label>
            <Select
              value={formData.projectId}
              onValueChange={(value) => setFormData(prev => ({ ...prev, projectId: value }))}
              placeholder="Select a recipe book"
              options={projectOptions}
            />
          </View>

          <View style={styles.row}>
            <View style={styles.halfField}>
              <Label>Prep Time (minutes)</Label>
              <Input
                value={formData.prepTime}
                onChangeText={(value) => setFormData(prev => ({ ...prev, prepTime: value }))}
                placeholder="30"
                keyboardType="numeric"
              />
            </View>
            <View style={styles.halfField}>
              <Label>Cook Time (minutes)</Label>
              <Input
                value={formData.cookTime}
                onChangeText={(value) => setFormData(prev => ({ ...prev, cookTime: value }))}
                placeholder="45"
                keyboardType="numeric"
              />
            </View>
          </View>

          <View style={styles.row}>
            <View style={styles.halfField}>
              <Label>Servings</Label>
              <Input
                value={formData.servings}
                onChangeText={(value) => setFormData(prev => ({ ...prev, servings: value }))}
                placeholder="4"
                keyboardType="numeric"
              />
            </View>
            <View style={styles.halfField}>
              <Label>Difficulty</Label>
              <Select
                value={formData.difficulty}
                onValueChange={(value) => setFormData(prev => ({ ...prev, difficulty: value }))}
                options={difficultyOptions}
              />
            </View>
          </View>

          <View style={styles.field}>
            <Label>Tags (comma-separated)</Label>
            <Input
              value={formData.tags}
              onChangeText={(value) => setFormData(prev => ({ ...prev, tags: value }))}
              placeholder="vegetarian, quick, family-favorite"
            />
          </View>

          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>Ingredients</Text>
              <Button
                title="Add Ingredient"
                onPress={addIngredient}
                size="sm"
                variant="outline"
              />
            </View>
            {formData.ingredients.map((ingredient, index) => (
              <View key={index} style={styles.listItem}>
                <Input
                  value={ingredient}
                  onChangeText={(value) => updateIngredient(index, value)}
                  placeholder={`Ingredient ${index + 1}`}
                  style={styles.listInput}
                />
                {formData.ingredients.length > 1 && (
                  <Button
                    title="×"
                    onPress={() => removeIngredient(index)}
                    size="sm"
                    variant="destructive"
                    style={styles.removeButton}
                  />
                )}
              </View>
            ))}
          </View>

          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>Instructions</Text>
              <Button
                title="Add Step"
                onPress={addInstruction}
                size="sm"
                variant="outline"
              />
            </View>
            {formData.instructions.map((instruction, index) => (
              <View key={index} style={styles.listItem}>
                <Text style={styles.stepNumber}>{index + 1}.</Text>
                <Textarea
                  value={instruction}
                  onChangeText={(value) => updateInstruction(index, value)}
                  placeholder={`Step ${index + 1}`}
                  style={styles.listTextarea}
                />
                {formData.instructions.length > 1 && (
                  <Button
                    title="×"
                    onPress={() => removeInstruction(index)}
                    size="sm"
                    variant="destructive"
                    style={styles.removeButton}
                  />
                )}
              </View>
            ))}
          </View>

          <Button
            title="Save Recipe"
            onPress={handleSubmit}
            style={styles.submitButton}
          />
        </View>
      </Card>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  card: {
    margin: 16,
  },
  content: {
    padding: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 24,
  },
  field: {
    marginBottom: 16,
  },
  row: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 16,
  },
  halfField: {
    flex: 1,
  },
  section: {
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  listItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
    gap: 8,
  },
  stepNumber: {
    fontSize: 14,
    fontWeight: '500',
    paddingTop: 12,
    minWidth: 20,
  },
  listInput: {
    flex: 1,
  },
  listTextarea: {
    flex: 1,
    minHeight: 60,
  },
  removeButton: {
    width: 32,
    height: 32,
    minWidth: 32,
  },
  submitButton: {
    marginTop: 16,
  },
});
