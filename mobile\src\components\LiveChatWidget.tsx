import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, TextInput, KeyboardAvoidingView, Platform, Alert } from 'react-native';
import { Colors, Spacing, BorderRadius, API_URL } from '../lib/constants';
import { Button } from './ui/button';
import { Card, CardContent, CardHeader } from './ui/card';
import { useAuth } from '../hooks/use-auth';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Icon from 'react-native-vector-icons/Feather';

interface ChatMessage {
  id: string;
  message: string;
  sender: 'user' | 'support';
  timestamp: Date;
}

interface LiveChatWidgetProps {
  isEnabled?: boolean;
  position?: 'bottom-right' | 'bottom-left';
}

export function LiveChatWidget({ isEnabled = true, position = 'bottom-right' }: LiveChatWidgetProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [currentMessage, setCurrentMessage] = useState('');
  const [isConnected, setIsConnected] = useState(false);
  const [hasStartedChat, setHasStartedChat] = useState(false);
  const [currentTicketId, setCurrentTicketId] = useState<number | null>(null);
  const [isCreatingTicket, setIsCreatingTicket] = useState(false);
  const [showTicketPrompt, setShowTicketPrompt] = useState(false);
  const { user } = useAuth();

  // Check if external chat service is configured
  useEffect(() => {
    // This would check if Zendesk Chat, Intercom, or other service is configured
    const checkChatService = () => {
      // Check if external service is configured (same as web)
      const hasExternalService = process.env.EXPO_PUBLIC_SUPPORT_SERVICE === 'zendesk' ||
                                 process.env.EXPO_PUBLIC_SUPPORT_SERVICE === 'intercom';
      setIsConnected(hasExternalService);
    };

    checkChatService();
  }, []);

  const handleSendMessage = async () => {
    if (!currentMessage.trim()) return;

    const newMessage: ChatMessage = {
      id: Date.now().toString(),
      message: currentMessage,
      sender: 'user',
      timestamp: new Date()
    };

    setMessages(prev => [...prev, newMessage]);
    const messageToSend = currentMessage;
    setCurrentMessage('');
    setHasStartedChat(true);

    // Show automated response based on connection status (same as web)
    setTimeout(() => {
      if (isConnected) {
        // If connected to external service, show live chat response
        const supportMessage: ChatMessage = {
          id: (Date.now() + 1).toString(),
          message: "Thank you for contacting us! A support representative will be with you shortly.",
          sender: 'support',
          timestamp: new Date()
        };
        setMessages(prev => [...prev, supportMessage]);
      } else {
        // If not connected, offer to create a support ticket after a few messages
        const messageCount = messages.filter(m => m.sender === 'user').length + 1;

        if (messageCount >= 2 && !currentTicketId && !showTicketPrompt) {
          setShowTicketPrompt(true);
          const ticketPromptMessage: ChatMessage = {
            id: (Date.now() + 1).toString(),
            message: "I notice you have a few questions. Would you like me to create a support ticket so our team can help you more effectively?",
            sender: 'support',
            timestamp: new Date()
          };
          setMessages(prev => [...prev, ticketPromptMessage]);
        } else {
          const supportMessage: ChatMessage = {
            id: (Date.now() + 1).toString(),
            message: "Thank you for your message. Our support team will get back to you soon. You can also check our Help Center for quick answers.",
            sender: 'support',
            timestamp: new Date()
          };
          setMessages(prev => [...prev, supportMessage]);
        }
      }
    }, 1000);
  };

  const createSupportTicket = async () => {
    if (!user?.email) {
      Alert.alert('Error', 'Please log in to create a support ticket');
      return;
    }

    setIsCreatingTicket(true);
    try {
      const token = await AsyncStorage.getItem('token');
      const chatHistory = messages.map(m => `${m.sender}: ${m.message}`).join('\n');

      const response = await fetch(`${API_URL}/support/tickets`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          subject: 'Chat Support Request',
          description: `Support request from live chat:\n\n${chatHistory}`,
          priority: 'medium',
          userEmail: user.email
        }),
      });

      if (response.ok) {
        const data = await response.json();
        setCurrentTicketId(data.ticket.id);
        setShowTicketPrompt(false);

        const ticketMessage: ChatMessage = {
          id: (Date.now() + 1).toString(),
          message: `Great! I've created support ticket #${data.ticket.id} for you. Our team will review your request and get back to you soon.`,
          sender: 'support',
          timestamp: new Date()
        };
        setMessages(prev => [...prev, ticketMessage]);
      } else {
        throw new Error('Failed to create ticket');
      }
    } catch (error) {
      console.error('Error creating support ticket:', error);
      Alert.alert('Error', 'Failed to create support ticket. Please try again.');
    } finally {
      setIsCreatingTicket(false);
    }
  };

  const handleStartChat = () => {
    setHasStartedChat(true);
    const welcomeMessage: ChatMessage = {
      id: 'welcome',
      message: "Hello! How can we help you today?",
      sender: 'support',
      timestamp: new Date()
    };
    setMessages([welcomeMessage]);
  };

  if (!isEnabled) return null;

  const positionStyle = position === 'bottom-right'
    ? { bottom: 20, right: 20 }
    : { bottom: 20, left: 20 };

  return (
    <View style={[styles.container, positionStyle]}>
      {!isOpen ? (
        // Chat button
        <TouchableOpacity
          style={styles.chatButton}
          onPress={() => setIsOpen(true)}
        >
          <Icon name="message-square" size={24} color={Colors.primaryForeground} />
        </TouchableOpacity>
      ) : (
        // Chat window
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={styles.chatWindow}
        >
          <Card style={[styles.card, isMinimized && styles.minimizedCard]}>
            <CardHeader style={styles.header}>
              <View style={styles.headerContent}>
                <Text style={styles.headerTitle}>
                  {isConnected ? 'Live Support Chat' : 'Support Chat'}
                </Text>
                <View style={styles.headerActions}>
                  <TouchableOpacity
                    style={styles.headerButton}
                    onPress={() => setIsMinimized(!isMinimized)}
                  >
                    <Icon name="minus" size={16} color={Colors.primaryForeground} />
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={styles.headerButton}
                    onPress={() => setIsOpen(false)}
                  >
                    <Icon name="x" size={16} color={Colors.primaryForeground} />
                  </TouchableOpacity>
                </View>
              </View>
            </CardHeader>

            {!isMinimized && (
              <CardContent style={styles.content}>
                {!hasStartedChat ? (
                  // Welcome screen
                  <View style={styles.welcomeScreen}>
                    <Icon name="message-square" size={48} color={Colors.mutedForeground} />
                    <Text style={styles.welcomeTitle}>Need Help?</Text>
                    <Text style={styles.welcomeDescription}>
                      Start a conversation with our support team. We're here to help!
                    </Text>
                    {!isConnected && (
                      <Text style={styles.offlineNotice}>
                        Live chat is currently offline. Messages will be sent as support tickets.
                      </Text>
                    )}
                    <Button onPress={handleStartChat} style={styles.startChatButton}>
                      <Text style={styles.startChatText}>Start Chat</Text>
                    </Button>
                  </View>
                ) : (
                  <>
                    {/* Messages area */}
                    <ScrollView style={styles.messagesContainer} showsVerticalScrollIndicator={false}>
                      {messages.map((message) => (
                        <View
                          key={message.id}
                          style={[
                            styles.messageWrapper,
                            message.sender === 'user' ? styles.userMessageWrapper : styles.supportMessageWrapper
                          ]}
                        >
                          <View
                            style={[
                              styles.messageBubble,
                              message.sender === 'user' ? styles.userMessage : styles.supportMessage
                            ]}
                          >
                            <Text style={[
                              styles.messageText,
                              message.sender === 'user' ? styles.userMessageText : styles.supportMessageText
                            ]}>
                              {message.message}
                            </Text>
                            <Text style={[
                              styles.messageTime,
                              message.sender === 'user' ? styles.userMessageTime : styles.supportMessageTime
                            ]}>
                              {message.timestamp.toLocaleTimeString([], {
                                hour: '2-digit',
                                minute: '2-digit'
                              })}
                            </Text>
                          </View>
                        </View>
                      ))}
                    </ScrollView>

                    {/* Ticket creation prompt */}
                    {showTicketPrompt && !currentTicketId && (
                      <View style={styles.ticketPrompt}>
                        <Text style={styles.ticketPromptText}>Create a support ticket?</Text>
                        <View style={styles.ticketPromptButtons}>
                          <TouchableOpacity
                            style={[styles.ticketButton, styles.ticketButtonSecondary]}
                            onPress={() => setShowTicketPrompt(false)}
                          >
                            <Text style={styles.ticketButtonTextSecondary}>No, thanks</Text>
                          </TouchableOpacity>
                          <TouchableOpacity
                            style={[styles.ticketButton, styles.ticketButtonPrimary]}
                            onPress={createSupportTicket}
                            disabled={isCreatingTicket}
                          >
                            <Text style={styles.ticketButtonTextPrimary}>
                              {isCreatingTicket ? 'Creating...' : 'Yes, create ticket'}
                            </Text>
                          </TouchableOpacity>
                        </View>
                      </View>
                    )}

                    {/* Input area */}
                    <View style={styles.inputContainer}>
                      <View style={styles.inputWrapper}>
                        <TextInput
                          style={styles.textInput}
                          value={currentMessage}
                          onChangeText={setCurrentMessage}
                          placeholder="Type your message..."
                          placeholderTextColor={Colors.mutedForeground}
                          multiline
                          maxLength={500}
                        />
                        <TouchableOpacity
                          style={[styles.sendButton, !currentMessage.trim() && styles.sendButtonDisabled]}
                          onPress={handleSendMessage}
                          disabled={!currentMessage.trim()}
                        >
                          <Icon
                            name="send"
                            size={16}
                            color={currentMessage.trim() ? Colors.primaryForeground : Colors.mutedForeground}
                          />
                        </TouchableOpacity>
                      </View>
                      {!isConnected && (
                        <Text style={styles.inputNotice}>
                          Messages will be converted to support tickets
                        </Text>
                      )}
                    </View>
                  </>
                )}
              </CardContent>
            )}
          </Card>
        </KeyboardAvoidingView>
      )}
    </View>
  );
}

// Hook to conditionally render the chat widget
export function useLiveChatWidget() {
  const [shouldShow, setShouldShow] = useState(false);

  useEffect(() => {
    // Only show chat widget on certain pages or for certain users
    // For mobile, we'll show it on most pages since navigation is different
    setShouldShow(true);
  }, []);

  return shouldShow;
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    zIndex: 1000,
  },
  chatButton: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: Colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  chatWindow: {
    width: 320,
    height: 400,
  },
  card: {
    flex: 1,
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 16,
  },
  minimizedCard: {
    height: 48,
  },
  header: {
    backgroundColor: Colors.primary,
    borderTopLeftRadius: BorderRadius.lg,
    borderTopRightRadius: BorderRadius.lg,
    padding: Spacing.md,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.primaryForeground,
  },
  headerActions: {
    flexDirection: 'row',
    gap: Spacing.sm,
  },
  headerButton: {
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
    padding: 0,
  },
  welcomeScreen: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: Spacing.lg,
  },
  welcomeTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.foreground,
    marginTop: Spacing.md,
    marginBottom: Spacing.sm,
  },
  welcomeDescription: {
    fontSize: 14,
    color: Colors.mutedForeground,
    textAlign: 'center',
    marginBottom: Spacing.md,
    lineHeight: 20,
  },
  offlineNotice: {
    fontSize: 12,
    color: '#ea580c',
    textAlign: 'center',
    marginBottom: Spacing.md,
  },
  startChatButton: {
    width: '100%',
  },
  startChatText: {
    color: Colors.primaryForeground,
    fontWeight: '500',
  },
  messagesContainer: {
    flex: 1,
    padding: Spacing.md,
  },
  messageWrapper: {
    marginBottom: Spacing.md,
  },
  userMessageWrapper: {
    alignItems: 'flex-end',
  },
  supportMessageWrapper: {
    alignItems: 'flex-start',
  },
  messageBubble: {
    maxWidth: '80%',
    padding: Spacing.md,
    borderRadius: BorderRadius.md,
  },
  userMessage: {
    backgroundColor: Colors.primary,
  },
  supportMessage: {
    backgroundColor: Colors.muted,
  },
  messageText: {
    fontSize: 14,
    lineHeight: 18,
  },
  userMessageText: {
    color: Colors.primaryForeground,
  },
  supportMessageText: {
    color: Colors.foreground,
  },
  messageTime: {
    fontSize: 10,
    marginTop: Spacing.xs,
    opacity: 0.7,
  },
  userMessageTime: {
    color: Colors.primaryForeground,
  },
  supportMessageTime: {
    color: Colors.mutedForeground,
  },
  inputContainer: {
    borderTopWidth: 1,
    borderTopColor: Colors.border,
    padding: Spacing.md,
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    gap: Spacing.sm,
  },
  textInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: BorderRadius.md,
    padding: Spacing.sm,
    maxHeight: 80,
    fontSize: 14,
    color: Colors.foreground,
    backgroundColor: Colors.background,
  },
  sendButton: {
    width: 40,
    height: 40,
    borderRadius: BorderRadius.md,
    backgroundColor: Colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sendButtonDisabled: {
    backgroundColor: Colors.muted,
  },
  inputNotice: {
    fontSize: 10,
    color: Colors.mutedForeground,
    marginTop: Spacing.xs,
  },
  ticketPrompt: {
    backgroundColor: Colors.muted,
    padding: Spacing.md,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
  ticketPromptText: {
    fontSize: 14,
    color: Colors.foreground,
    marginBottom: Spacing.sm,
    textAlign: 'center',
  },
  ticketPromptButtons: {
    flexDirection: 'row',
    gap: Spacing.sm,
  },
  ticketButton: {
    flex: 1,
    padding: Spacing.sm,
    borderRadius: BorderRadius.md,
    alignItems: 'center',
  },
  ticketButtonPrimary: {
    backgroundColor: Colors.primary,
  },
  ticketButtonSecondary: {
    backgroundColor: Colors.background,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  ticketButtonTextPrimary: {
    color: Colors.primaryForeground,
    fontSize: 12,
    fontWeight: '500',
  },
  ticketButtonTextSecondary: {
    color: Colors.foreground,
    fontSize: 12,
  },
});
