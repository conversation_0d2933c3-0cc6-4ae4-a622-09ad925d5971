import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  ActivityIndicator
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useLocation } from '../lib/router';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useToast } from '../hooks/use-toast';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { useAuth } from '../hooks/use-auth';
import { Colors, Spacing, BorderRadius, API_URL } from '../lib/constants';
import { Picker } from '@react-native-picker/picker';
import { ImageUpload } from '../components/ui/image-upload';
import Icon from 'react-native-vector-icons/MaterialIcons';

interface Recipe {
  id: number;
  projectId: number;
  title: string;
  description: string;
  category: string;
  ingredients: Array<{
    name: string;
    amount: number;
    unit: string;
  }>;
  instructions: string[];
  tags: string[];
  measurementSystem: 'us' | 'metric';
  images: string[];
  status: string;
  createdAt: string;
  updatedAt: string;
}

interface Project {
  id: number;
  name: string;
  organizerId: number;
  pricingTier?: string;
  maxRecipes?: number;
  recipes?: any[];
}

interface RecipeFormData {
  projectId: number;
  title: string;
  description: string;
  category: string;
  ingredients: Array<{
    name: string;
    amount: number;
    unit: string;
  }>;
  instructions: string[];
  tags: string[];
  measurementSystem: 'us' | 'metric';
  images: string[];
}

const categories = [
  "Main Dishes",
  "Side Dishes",
  "Soups",
  "Salads",
  "Desserts",
  "Breakfast",
  "Snacks",
  "Beverages",
  "Appetizers",
  "Breads",
  "Sauces",
  "Other"
];

export default function EditRecipeScreen() {
  const [location, setLocation] = useLocation();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { user } = useAuth();

  // Extract recipe ID from location
  const recipeId = location.split('/')[2]; // /recipes/{id}/edit

  // Form state
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [category, setCategory] = useState('Main Dishes');
  const [selectedProjectId, setSelectedProjectId] = useState<number | null>(null);
  const [ingredients, setIngredients] = useState([{ name: '', amount: 1, unit: 'cup' }]);
  const [instructions, setInstructions] = useState(['']);
  const [tags, setTags] = useState('');
  const [measurementSystem, setMeasurementSystem] = useState<'us' | 'metric'>('us');
  const [selectedImages, setSelectedImages] = useState<string[]>([]);

  // Fetch recipe data
  const { data: recipe, isLoading: isLoadingRecipe } = useQuery<Recipe>({
    queryKey: ['recipe', recipeId],
    queryFn: async () => {
      const token = await AsyncStorage.getItem('token');
      if (!token) {
        throw new Error('Not authenticated');
      }

      const response = await fetch(`${API_URL}/contributor/recipes/${recipeId}`, {
        headers: {
          "Authorization": `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to fetch recipe');
      }

      const data = await response.json();
      return data.recipe;
    },
    enabled: !!recipeId
  });

  // Fetch recipe books
  const { data: projects, isLoading: isLoadingProjects } = useQuery<Project[]>({
    queryKey: ['projects', user?.role],
    queryFn: async () => {
      const token = await AsyncStorage.getItem('token');
      if (!token) {
        throw new Error('Not authenticated');
      }

      // Use different endpoints based on user role
      let endpoint = '';
      if (user?.role === 'organizer') {
        endpoint = `${API_URL}/organizer/my-projects`;
      } else if (user?.role === 'contributor') {
        endpoint = `${API_URL}/contributor/projects`;
      } else if (user?.role === 'admin') {
        endpoint = `${API_URL}/organizer/all-projects`;
      } else {
        throw new Error('User role not supported');
      }

      const response = await fetch(endpoint, {
        headers: {
          "Authorization": `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to fetch recipe books');
      }

      const data = await response.json();
      return data.projects || [];
    },
    enabled: !!user?.role
  });

  // Populate form when recipe data is loaded
  useEffect(() => {
    if (recipe) {
      setTitle(recipe.title);
      setDescription(recipe.description);
      setCategory(recipe.category || 'Main Dishes');
      setSelectedProjectId(recipe.projectId);
      setIngredients(recipe.ingredients.length > 0 ? recipe.ingredients : [{ name: '', amount: 1, unit: 'cup' }]);
      setInstructions(recipe.instructions.length > 0 ? recipe.instructions : ['']);
      setTags(recipe.tags.join(', '));
      setMeasurementSystem(recipe.measurementSystem || 'us');
      setSelectedImages(recipe.images || []);
    }
  }, [recipe]);

  const isLoading = isLoadingRecipe || isLoadingProjects;
