import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  ActivityIndicator
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useLocation } from '../lib/router';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useToast } from '../hooks/use-toast';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { useAuth } from '../hooks/use-auth';
import { Colors, Spacing, BorderRadius, API_URL } from '../lib/constants';
import { Picker } from '@react-native-picker/picker';
import { ImageUpload } from '../components/ui/image-upload';
import Icon from 'react-native-vector-icons/MaterialIcons';

interface Recipe {
  id: number;
  projectId: number;
  title: string;
  description: string;
  category: string;
  ingredients: Array<{
    name: string;
    amount: number;
    unit: string;
  }>;
  instructions: string[];
  tags: string[];
  measurementSystem: 'us' | 'metric';
  images: string[];
  status: string;
  createdAt: string;
  updatedAt: string;
}

interface Project {
  id: number;
  name: string;
  organizerId: number;
  pricingTier?: string;
  maxRecipes?: number;
  recipes?: any[];
}

interface RecipeFormData {
  projectId: number;
  title: string;
  description: string;
  category: string;
  ingredients: Array<{
    name: string;
    amount: number;
    unit: string;
  }>;
  instructions: string[];
  tags: string[];
  measurementSystem: 'us' | 'metric';
  images: string[];
}

const categories = [
  "Main Dishes",
  "Side Dishes",
  "Soups",
  "Salads",
  "Desserts",
  "Breakfast",
  "Snacks",
  "Beverages",
  "Appetizers",
  "Breads",
  "Sauces",
  "Other"
];

export default function EditRecipeScreen() {
  const [location, setLocation] = useLocation();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { user } = useAuth();

  // Extract recipe ID from location
  const recipeId = location.split('/')[2]; // /recipes/{id}/edit

  // Form state
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [category, setCategory] = useState('Main Dishes');
  const [selectedProjectId, setSelectedProjectId] = useState<number | null>(null);
  const [ingredients, setIngredients] = useState([{ name: '', amount: 1, unit: 'cup' }]);
  const [instructions, setInstructions] = useState(['']);
  const [tags, setTags] = useState('');
  const [measurementSystem, setMeasurementSystem] = useState<'us' | 'metric'>('us');
  const [selectedImages, setSelectedImages] = useState<string[]>([]);

  // Fetch recipe data
  const { data: recipe, isLoading: isLoadingRecipe } = useQuery<Recipe>({
    queryKey: ['recipe', recipeId],
    queryFn: async () => {
      const token = await AsyncStorage.getItem('token');
      if (!token) {
        throw new Error('Not authenticated');
      }

      const response = await fetch(`${API_URL}/contributor/recipes/${recipeId}`, {
        headers: {
          "Authorization": `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to fetch recipe');
      }

      const data = await response.json();
      return data.recipe;
    },
    enabled: !!recipeId
  });

  // Fetch recipe books
  const { data: projects, isLoading: isLoadingProjects } = useQuery<Project[]>({
    queryKey: ['projects', user?.role],
    queryFn: async () => {
      const token = await AsyncStorage.getItem('token');
      if (!token) {
        throw new Error('Not authenticated');
      }

      // Use different endpoints based on user role
      let endpoint = '';
      if (user?.role === 'organizer') {
        endpoint = `${API_URL}/organizer/my-projects`;
      } else if (user?.role === 'contributor') {
        endpoint = `${API_URL}/contributor/projects`;
      } else if (user?.role === 'admin') {
        endpoint = `${API_URL}/organizer/all-projects`;
      } else {
        throw new Error('User role not supported');
      }

      const response = await fetch(endpoint, {
        headers: {
          "Authorization": `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to fetch recipe books');
      }

      const data = await response.json();
      return data.projects || [];
    },
    enabled: !!user?.role
  });

  // Populate form when recipe data is loaded
  useEffect(() => {
    if (recipe) {
      setTitle(recipe.title);
      setDescription(recipe.description);
      setCategory(recipe.category || 'Main Dishes');
      setSelectedProjectId(recipe.projectId);
      setIngredients(recipe.ingredients.length > 0 ? recipe.ingredients : [{ name: '', amount: 1, unit: 'cup' }]);
      setInstructions(recipe.instructions.length > 0 ? recipe.instructions : ['']);
      setTags(recipe.tags.join(', '));
      setMeasurementSystem(recipe.measurementSystem || 'us');
      setSelectedImages(recipe.images || []);
    }
  }, [recipe]);

  const isLoading = isLoadingRecipe || isLoadingProjects;

  // Helper functions for managing ingredients and instructions
  const addIngredient = () => {
    if (ingredients.length < 15) {
      setIngredients([...ingredients, { name: '', amount: 1, unit: 'cup' }]);
    }
  };

  const removeIngredient = (index: number) => {
    if (ingredients.length > 1) {
      setIngredients(ingredients.filter((_, i) => i !== index));
    } else {
      toast({
        title: "Cannot remove",
        description: "At least one ingredient is required",
        variant: "destructive",
      });
    }
  };

  const updateIngredient = (index: number, field: string, value: any) => {
    const updated = ingredients.map((ingredient, i) =>
      i === index ? { ...ingredient, [field]: value } : ingredient
    );
    setIngredients(updated);
  };

  const addInstruction = () => {
    if (instructions.length < 15) {
      setInstructions([...instructions, '']);
    }
  };

  const removeInstruction = (index: number) => {
    if (instructions.length > 1) {
      setInstructions(instructions.filter((_, i) => i !== index));
    } else {
      toast({
        title: "Cannot remove",
        description: "At least one instruction is required",
        variant: "destructive",
      });
    }
  };

  const updateInstruction = (index: number, value: string) => {
    const updated = instructions.map((instruction, i) =>
      i === index ? value : instruction
    );
    setInstructions(updated);
  };

  const handleImageUpload = (urls: string[]) => {
    setSelectedImages(urls);
  };

  // Update recipe mutation
  const updateRecipeMutation = useMutation({
    mutationFn: async (data: RecipeFormData) => {
      const token = await AsyncStorage.getItem('token');
      if (!token) {
        throw new Error('Not authenticated');
      }

      // Process images to ensure consistent format - server expects images WITHOUT recipes/ prefix
      const processedImages = selectedImages.map((url: string) => {
        // If it's already just a key (not a full URL), return as is
        if (!url.includes('http')) {
          // Remove recipes/ prefix if it exists (server will store without prefix)
          return url.replace('recipes/', '');
        }

        // Extract the path after the bucket name
        const bucketNameIndex = url.lastIndexOf('recipe-book-images-bucket');
        if (bucketNameIndex === -1) return url;

        const pathStart = url.indexOf('/', bucketNameIndex);
        // Remove recipes/ prefix from the extracted path
        const path = pathStart !== -1 ? url.substring(pathStart + 1) : url;
        return path.replace('recipes/', '');
      });

      const response = await fetch(`${API_URL}/organizer/recipes/${recipeId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          ...data,
          images: processedImages,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to update recipe');
      }

      const result = await response.json();
      queryClient.invalidateQueries({ queryKey: ['recipe', recipeId] });
      queryClient.invalidateQueries({ queryKey: ['recipes'] });
      return result;
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Recipe updated successfully",
      });
      setLocation(`/recipe-books/${selectedProjectId}`);
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update recipe",
        variant: "destructive",
      });
    },
  });

  const handleSubmit = () => {
    if (!title.trim() || !description.trim() || !selectedProjectId) {
      Alert.alert('Error', 'Please fill in all required fields and select a recipe book');
      return;
    }

    const validIngredients = ingredients.filter(ing => ing.name.trim());
    const validInstructions = instructions.filter(inst => inst.trim());

    if (validIngredients.length === 0 || validInstructions.length === 0) {
      Alert.alert('Error', 'Please add at least one ingredient and one instruction');
      return;
    }

    const formData: RecipeFormData = {
      projectId: selectedProjectId,
      title: title.trim(),
      description: description.trim(),
      category,
      ingredients: validIngredients,
      instructions: validInstructions,
      tags: tags.split(',').map(tag => tag.trim()).filter(Boolean),
      measurementSystem,
      images: selectedImages,
    };

    updateRecipeMutation.mutate(formData);
  };

  if (!user) {
    return null;
  }

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={Colors.primary} />
        <Text style={styles.loadingText}>Loading...</Text>
      </View>
    );
  }

  if (!recipe) {
    return (
      <View style={styles.container}>
        <Card style={styles.errorCard}>
          <CardContent>
            <Text style={styles.errorText}>Recipe not found.</Text>
          </CardContent>
        </Card>
      </View>
    );
  }

  // Check if user is authorized to edit
  const canEdit = user.id === recipe.contributor?.id || user.id === recipe.project?.organizerId || user.role === 'admin';
  if (!canEdit) {
    return (
      <View style={styles.container}>
        <Card style={styles.errorCard}>
          <CardContent>
            <Text style={styles.errorText}>
              You are not authorized to edit this recipe.
            </Text>
          </CardContent>
        </Card>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>Edit Recipe</Text>
        <Button
          variant="outline"
          onPress={() => setLocation(`/recipe-books/${selectedProjectId}`)}
          size="sm"
        >
          Back to Recipe Book
        </Button>
      </View>

      <Card style={styles.formCard}>
        <CardHeader>
          <Text style={styles.formTitle}>Edit Recipe Details</Text>
        </CardHeader>
        <CardContent>
          {/* Recipe Book Selection */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Recipe Book *</Text>
            <View style={styles.pickerContainer}>
              <Picker
                selectedValue={selectedProjectId || ''}
                onValueChange={(value) => setSelectedProjectId(Number(value) || null)}
                style={styles.picker}
              >
                <Picker.Item label="Select a recipe book" value="" />
                {projects?.map((project) => (
                  <Picker.Item key={project.id} label={project.name} value={project.id} />
                ))}
              </Picker>
            </View>
          </View>

          {/* Basic Info */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Title *</Text>
            <Input
              value={title}
              onChangeText={setTitle}
              placeholder="Enter recipe title"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Description *</Text>
            <TextInput
              style={styles.textArea}
              value={description}
              onChangeText={setDescription}
              placeholder="Enter recipe description"
              multiline
              numberOfLines={3}
              textAlignVertical="top"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Category *</Text>
            <View style={styles.pickerContainer}>
              <Picker
                selectedValue={category}
                onValueChange={setCategory}
                style={styles.picker}
              >
                {categories.map((cat) => (
                  <Picker.Item key={cat} label={cat} value={cat} />
                ))}
              </Picker>
            </View>
          </View>

          {/* Measurement System */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Measurement System</Text>
            <View style={styles.measurementContainer}>
              <TouchableOpacity
                style={[styles.measurementButton, measurementSystem === 'us' && styles.measurementButtonActive]}
                onPress={() => setMeasurementSystem('us')}
              >
                <Text style={[styles.measurementButtonText, measurementSystem === 'us' && styles.measurementButtonTextActive]}>
                  US (cups, oz, lbs)
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.measurementButton, measurementSystem === 'metric' && styles.measurementButtonActive]}
                onPress={() => setMeasurementSystem('metric')}
              >
                <Text style={[styles.measurementButtonText, measurementSystem === 'metric' && styles.measurementButtonTextActive]}>
                  Metric (ml, g, kg)
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Tags (comma separated)</Text>
            <Input
              value={tags}
              onChangeText={setTags}
              placeholder="e.g. vegetarian, quick, healthy"
            />
          </View>

          {/* Ingredients Section */}
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>Ingredients *</Text>
              <Button
                variant="outline"
                size="sm"
                onPress={addIngredient}
                disabled={ingredients.length >= 15}
              >
                <Icon name="add" size={16} color={Colors.foreground} />
                <Text style={styles.addButtonSmallText}>Add</Text>
              </Button>
            </View>
            {ingredients.map((ingredient, index) => (
              <View key={index} style={styles.ingredientRow}>
                <View style={styles.ingredientName}>
                  <Input
                    value={ingredient.name}
                    onChangeText={(text) => updateIngredient(index, 'name', text)}
                    placeholder="Ingredient name"
                  />
                </View>
                <View style={styles.ingredientAmount}>
                  <Input
                    value={ingredient.amount.toString()}
                    onChangeText={(text) => updateIngredient(index, 'amount', parseFloat(text) || 0)}
                    keyboardType="numeric"
                    placeholder="1"
                  />
                </View>
                <View style={styles.ingredientUnit}>
                  <View style={styles.pickerContainer}>
                    <Picker
                      selectedValue={ingredient.unit}
                      onValueChange={(value) => updateIngredient(index, 'unit', value)}
                      style={styles.pickerSmall}
                    >
                      <Picker.Item label="cup" value="cup" />
                      <Picker.Item label="tbsp" value="tbsp" />
                      <Picker.Item label="tsp" value="tsp" />
                      <Picker.Item label="oz" value="oz" />
                      <Picker.Item label="lb" value="lb" />
                      <Picker.Item label="g" value="g" />
                      <Picker.Item label="kg" value="kg" />
                      <Picker.Item label="ml" value="ml" />
                      <Picker.Item label="l" value="l" />
                      <Picker.Item label="piece" value="piece" />
                      <Picker.Item label="clove" value="clove" />
                      <Picker.Item label="pinch" value="pinch" />
                    </Picker>
                  </View>
                </View>
                {ingredients.length > 1 && (
                  <TouchableOpacity
                    onPress={() => removeIngredient(index)}
                    style={styles.removeButton}
                  >
                    <Icon name="remove" size={20} color={Colors.destructive} />
                  </TouchableOpacity>
                )}
              </View>
            ))}
          </View>

          {/* Instructions Section */}
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>Instructions *</Text>
              <Button
                variant="outline"
                size="sm"
                onPress={addInstruction}
                disabled={instructions.length >= 15}
              >
                <Icon name="add" size={16} color={Colors.foreground} />
                <Text style={styles.addButtonSmallText}>Add</Text>
              </Button>
            </View>
            {instructions.map((instruction, index) => (
              <View key={index} style={styles.instructionRow}>
                <View style={styles.stepNumber}>
                  <Text style={styles.stepNumberText}>{index + 1}</Text>
                </View>
                <View style={styles.instructionInput}>
                  <TextInput
                    style={styles.instructionTextArea}
                    value={instruction}
                    onChangeText={(text) => updateInstruction(index, text)}
                    placeholder={`Step ${index + 1} instructions`}
                    multiline
                    numberOfLines={2}
                    textAlignVertical="top"
                  />
                </View>
                {instructions.length > 1 && (
                  <TouchableOpacity
                    onPress={() => removeInstruction(index)}
                    style={styles.removeButton}
                  >
                    <Icon name="remove" size={20} color={Colors.destructive} />
                  </TouchableOpacity>
                )}
              </View>
            ))}
          </View>

          {/* Recipe Images */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Recipe Images</Text>
            <ImageUpload
              onUpload={handleImageUpload}
              maxFiles={3}
              acceptedFileTypes={['image/jpeg', 'image/png', 'image/webp']}
              initialFiles={selectedImages}
            />
          </View>

          {/* Form Actions */}
          <View style={styles.formActions}>
            <Button
              onPress={handleSubmit}
              disabled={updateRecipeMutation.isPending}
              style={styles.submitButton}
            >
              {updateRecipeMutation.isPending ? (
                <>
                  <ActivityIndicator size="small" color={Colors.primaryForeground} />
                  <Text style={styles.submitButtonText}>Saving...</Text>
                </>
              ) : (
                'Save Changes'
              )}
            </Button>
            <Button
              variant="outline"
              onPress={() => setLocation(`/recipe-books/${selectedProjectId}`)}
              style={styles.cancelButton}
            >
              Cancel
            </Button>
          </View>
        </CardContent>
      </Card>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.background,
  },
  loadingText: {
    marginTop: Spacing.md,
    fontSize: 16,
    color: Colors.foreground,
  },
  errorCard: {
    margin: Spacing.lg,
  },
  errorText: {
    fontSize: 16,
    color: Colors.destructive,
    textAlign: 'center',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: Spacing.lg,
    backgroundColor: Colors.card,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: Colors.foreground,
    flex: 1,
  },
  formCard: {
    margin: Spacing.lg,
    marginBottom: Spacing.md,
  },
  formTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.cardForeground,
  },
  inputGroup: {
    marginBottom: Spacing.lg,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.foreground,
    marginBottom: Spacing.sm,
  },
  textArea: {
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: BorderRadius.md,
    padding: Spacing.md,
    fontSize: 16,
    backgroundColor: Colors.card,
    color: Colors.foreground,
    minHeight: 80,
    textAlignVertical: 'top',
  },
  pickerContainer: {
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: BorderRadius.md,
    backgroundColor: Colors.card,
  },
  picker: {
    color: Colors.foreground,
  },
  pickerSmall: {
    color: Colors.foreground,
    fontSize: 14,
  },
  measurementContainer: {
    flexDirection: 'row',
    gap: Spacing.sm,
  },
  measurementButton: {
    flex: 1,
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.sm,
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: BorderRadius.md,
    backgroundColor: Colors.card,
    alignItems: 'center',
  },
  measurementButtonActive: {
    backgroundColor: Colors.primary,
    borderColor: Colors.primary,
  },
  measurementButtonText: {
    fontSize: 14,
    color: Colors.foreground,
    textAlign: 'center',
  },
  measurementButtonTextActive: {
    color: Colors.primaryForeground,
    fontWeight: '600',
  },
  section: {
    marginBottom: Spacing.xl,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.foreground,
  },
  addButtonSmallText: {
    color: Colors.foreground,
    marginLeft: Spacing.xs,
  },
  ingredientRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.sm,
    marginBottom: Spacing.md,
  },
  ingredientName: {
    flex: 2,
  },
  ingredientAmount: {
    flex: 1,
  },
  ingredientUnit: {
    flex: 1,
  },
  removeButton: {
    padding: Spacing.sm,
    justifyContent: 'center',
    alignItems: 'center',
  },
  instructionRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: Spacing.sm,
    marginBottom: Spacing.md,
  },
  stepNumber: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: Colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: Spacing.sm,
  },
  stepNumberText: {
    color: Colors.primaryForeground,
    fontWeight: 'bold',
    fontSize: 14,
  },
  instructionInput: {
    flex: 1,
  },
  instructionTextArea: {
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: BorderRadius.md,
    padding: Spacing.md,
    fontSize: 16,
    backgroundColor: Colors.card,
    color: Colors.foreground,
    minHeight: 60,
    textAlignVertical: 'top',
  },
  formActions: {
    gap: Spacing.md,
    marginTop: Spacing.lg,
  },
  submitButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.xs,
  },
  submitButtonText: {
    color: Colors.primaryForeground,
    marginLeft: Spacing.xs,
  },
  cancelButton: {
    marginBottom: 0,
  },
});