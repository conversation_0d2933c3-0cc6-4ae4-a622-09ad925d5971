import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { PricingCalculator } from '../components/pricing/pricing-calculator';
import { Card, CardContent, CardHeader } from '../components/ui/card';
import { Colors, Spacing, BorderRadius } from '../lib/constants';

// Constants matching web version exactly
const PricingTier = {
  SMALL: 'small',
  MEDIUM: 'medium',
  LARGE: 'large'
};

const PricingModel = {
  [PricingTier.SMALL]: {
    name: 'Small Cookbook',
    description: 'Perfect for family recipes',
    basePrice: 15.99,
    pricePerPage: 0.25,
    maxPages: 50
  },
  [PricingTier.MEDIUM]: {
    name: 'Medium Cookbook',
    description: 'Great for community collections',
    basePrice: 24.99,
    pricePerPage: 0.30,
    maxPages: 100
  },
  [PricingTier.LARGE]: {
    name: 'Large Cookbook',
    description: 'Professional cookbook publishing',
    basePrice: 39.99,
    pricePerPage: 0.35,
    maxPages: 200
  }
};

export function PricingCalculatorPage() {
  const [pricingInfo, setPricingInfo] = useState({
    tier: PricingTier.SMALL,
    basePrice: PricingModel[PricingTier.SMALL].basePrice,
    pagePrice: 0,
    totalPrice: PricingModel[PricingTier.SMALL].basePrice,
    estimatedPages: 20
  });

  return (
    <ScrollView style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>Pricing Calculator</Text>
        <Text style={styles.subtitle}>
          Estimate the cost of your cookbook based on size and number of recipes
        </Text>

        <View style={styles.grid}>
          <View style={styles.calculatorSection}>
            <PricingCalculator
              initialTier={PricingTier.SMALL}
              onPricingChange={setPricingInfo}
            />
          </View>

          <View style={styles.summarySection}>
            <Card style={styles.summaryCard}>
              <CardHeader style={styles.summaryHeader}>
                <Text style={styles.summaryTitle}>Price Summary</Text>
              </CardHeader>
              <CardContent style={styles.summaryContent}>
                <View style={styles.summaryRow}>
                  <Text style={styles.summaryLabel}>Selected Tier:</Text>
                  <Text style={styles.summaryValue}>
                    {PricingModel[pricingInfo.tier as keyof typeof PricingModel]?.name || 'Small Cookbook'}
                  </Text>
                </View>

                <View style={styles.summaryRow}>
                  <Text style={styles.summaryLabel}>Estimated Pages:</Text>
                  <Text style={styles.summaryValue}>{pricingInfo.estimatedPages}</Text>
                </View>

                <View style={styles.summaryRow}>
                  <Text style={styles.summaryLabel}>Base Price:</Text>
                  <Text style={styles.summaryValue}>${pricingInfo.basePrice.toFixed(2)}</Text>
                </View>

                <View style={styles.summaryRow}>
                  <Text style={styles.summaryLabel}>Page Cost:</Text>
                  <Text style={styles.summaryValue}>${pricingInfo.pagePrice.toFixed(2)}</Text>
                </View>

                <View style={[styles.summaryRow, styles.totalRow]}>
                  <Text style={styles.totalLabel}>Total:</Text>
                  <Text style={styles.totalValue}>${pricingInfo.totalPrice.toFixed(2)}</Text>
                </View>

                <View style={styles.features}>
                  <Text style={styles.featuresTitle}>What's Included:</Text>
                  <Text style={styles.featureItem}>• Professional design and layout</Text>
                  <Text style={styles.featureItem}>• High-quality printing</Text>
                  <Text style={styles.featureItem}>• Durable binding</Text>
                  <Text style={styles.featureItem}>• Custom cover design</Text>
                  <Text style={styles.featureItem}>• Recipe index</Text>
                </View>
              </CardContent>
            </Card>
          </View>
        </View>

        <View style={styles.infoSection}>
          <Text style={styles.infoTitle}>Additional Information</Text>
          <Text style={styles.infoText}>
            • All prices include professional design, layout, and printing
          </Text>
          <Text style={styles.infoText}>
            • Free shipping on orders over $50
          </Text>
          <Text style={styles.infoText}>
            • Bulk discounts available for orders of 10+ copies
          </Text>
          <Text style={styles.infoText}>
            • Custom cover design included with all tiers
          </Text>
          <Text style={styles.infoText}>
            • Digital PDF copy included with every order
          </Text>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  content: {
    padding: Spacing.lg,
  },
  title: {
    fontSize: 28,
    fontWeight: '700',
    color: Colors.foreground,
    textAlign: 'center',
    marginBottom: Spacing.xs,
  },
  subtitle: {
    fontSize: 16,
    color: Colors.mutedForeground,
    textAlign: 'center',
    marginBottom: Spacing.xl,
    lineHeight: 24,
  },
  grid: {
    gap: Spacing.lg,
  },
  calculatorSection: {
    flex: 1,
  },
  summarySection: {
    flex: 1,
  },
  summaryCard: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
  },
  summaryHeader: {
    padding: Spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.foreground,
  },
  summaryContent: {
    padding: Spacing.lg,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  summaryLabel: {
    fontSize: 14,
    color: Colors.mutedForeground,
  },
  summaryValue: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.foreground,
  },
  totalRow: {
    borderTopWidth: 1,
    borderTopColor: Colors.border,
    paddingTop: Spacing.sm,
    marginTop: Spacing.sm,
    marginBottom: Spacing.lg,
  },
  totalLabel: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.foreground,
  },
  totalValue: {
    fontSize: 20,
    fontWeight: '700',
    color: Colors.primary,
  },
  features: {
    marginTop: Spacing.md,
  },
  featuresTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.foreground,
    marginBottom: Spacing.sm,
  },
  featureItem: {
    fontSize: 14,
    color: Colors.mutedForeground,
    marginBottom: Spacing.xs,
    lineHeight: 20,
  },
  infoSection: {
    marginTop: Spacing.xl,
    padding: Spacing.lg,
    backgroundColor: Colors.muted,
    borderRadius: BorderRadius.lg,
  },
  infoTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.foreground,
    marginBottom: Spacing.md,
  },
  infoText: {
    fontSize: 14,
    color: Colors.mutedForeground,
    marginBottom: Spacing.sm,
    lineHeight: 20,
  },
});
