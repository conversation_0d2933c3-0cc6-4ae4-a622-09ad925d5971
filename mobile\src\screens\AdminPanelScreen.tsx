import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  TextInput,
  Modal
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useLocation } from '../lib/router';
import { useToast } from '../hooks/use-toast';
import { useAuth } from '../hooks/use-auth';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Colors, Spacing, BorderRadius, API_URL } from '../lib/constants';
import { Picker } from '@react-native-picker/picker';
import Icon from 'react-native-vector-icons/MaterialIcons';

interface User {
  id: number;
  name: string;
  email: string;
  role: string;
  createdAt: string;
}

interface Project {
  id: number;
  name: string;
  description: string;
  status: string;
  organizerId: number;
  organizer: {
    name: string;
    email: string;
  };
  contributors: any[];
  recipes: any[];
  createdAt: string;
}

interface SupportTicket {
  id: number;
  subject: string;
  description: string;
  status: 'open' | 'in_progress' | 'resolved' | 'closed';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  userEmail: string;
  createdAt: string;
  updatedAt: string;
  messages: Array<{
    id: number;
    message: string;
    authorType: 'user' | 'admin';
    createdAt: string;
  }>;
}

export default function AdminPanelScreen() {
  const [, setLocation] = useLocation();
  const { toast } = useToast();
  const { user } = useAuth();

  // State
  const [activeTab, setActiveTab] = useState<'users' | 'projects' | 'support' | 'intercom'>('users');
  const [users, setUsers] = useState<User[]>([]);
  const [projects, setProjects] = useState<Project[]>([]);
  const [supportTickets, setSupportTickets] = useState<SupportTicket[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedRoles, setSelectedRoles] = useState<Record<number, string>>({});
  const [deletingProjectId, setDeletingProjectId] = useState<number | null>(null);
  const [isInviteDialogOpen, setIsInviteDialogOpen] = useState(false);
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [inviteEmail, setInviteEmail] = useState('');
  const [inviteRole, setInviteRole] = useState('contributor');
  const [isSendingInvite, setIsSendingInvite] = useState(false);
  const [ticketFilter, setTicketFilter] = useState<'all' | 'open' | 'in_progress' | 'resolved' | 'closed'>('all');
  const [selectedTicket, setSelectedTicket] = useState<SupportTicket | null>(null);
  const [ticketReply, setTicketReply] = useState('');
  const [isSendingReply, setIsSendingReply] = useState(false);

  // Check if user is admin
  if (!user || user.role !== 'admin') {
    return (
      <View style={styles.container}>
        <Card style={styles.errorCard}>
          <CardContent>
            <Icon name="error" size={48} color={Colors.destructive} style={styles.errorIcon} />
            <Text style={styles.errorText}>
              Admin access required. You don't have permission to view this page.
            </Text>
            <Button
              onPress={() => setLocation("/")}
              style={styles.backButton}
            >
              Back to Home
            </Button>
          </CardContent>
        </Card>
      </View>
    );
  }

  useEffect(() => {
    if (activeTab === 'users') {
      fetchUsers();
    } else if (activeTab === 'projects') {
      fetchProjects();
    } else if (activeTab === 'support') {
      fetchSupportTickets();
    }
  }, [activeTab, ticketFilter]);

  const fetchUsers = async () => {
    try {
      setIsLoading(true);
      const token = await AsyncStorage.getItem('token');
      const response = await fetch(`${API_URL}/admin/users`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setUsers(data.users);
      } else {
        throw new Error('Failed to fetch users');
      }
    } catch (error) {
      console.error('Error fetching users:', error);
      toast({
        title: "Error",
        description: "Failed to fetch users",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const fetchProjects = async () => {
    try {
      setIsLoading(true);
      const token = await AsyncStorage.getItem('token');
      const response = await fetch(`${API_URL}/admin/projects`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setProjects(data.projects);
      } else {
        throw new Error('Failed to fetch projects');
      }
    } catch (error) {
      console.error('Error fetching projects:', error);
      toast({
        title: "Error",
        description: "Failed to fetch projects",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const fetchSupportTickets = async () => {
    try {
      setIsLoading(true);
      const token = await AsyncStorage.getItem('token');
      const params = new URLSearchParams({
        page: '1',
        limit: '50'
      });

      if (ticketFilter && ticketFilter !== 'all') {
        params.append('status', ticketFilter);
      }

      const response = await fetch(`${API_URL}/support/admin/tickets?${params}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setSupportTickets(data.tickets);
      } else {
        throw new Error('Failed to fetch support tickets');
      }
    } catch (error) {
      console.error('Error fetching support tickets:', error);
      toast({
        title: "Error",
        description: "Failed to fetch support tickets",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleRoleChange = async (userId: number, newRole: string) => {
    try {
      const token = await AsyncStorage.getItem('token');
      const response = await fetch(`${API_URL}/admin/users/${userId}/role`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({ role: newRole }),
      });

      if (response.ok) {
        // Update local state
        setUsers(users.map(user =>
          user.id === userId ? { ...user, role: newRole } : user
        ));
        toast({
          title: "Success",
          description: "User role updated successfully",
        });
      } else {
        throw new Error('Failed to update user role');
      }
    } catch (error) {
      console.error('Error updating user role:', error);
      toast({
        title: "Error",
        description: "Failed to update user role",
        variant: "destructive",
      });
    }
  };

  const handleDeleteProject = async (projectId: number) => {
    Alert.alert(
      'Delete Project',
      'Are you sure you want to delete this project? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              setDeletingProjectId(projectId);
              const token = await AsyncStorage.getItem('token');
              const response = await fetch(`${API_URL}/admin/projects/${projectId}`, {
                method: 'DELETE',
                headers: {
                  'Authorization': `Bearer ${token}`,
                },
              });

              if (response.ok) {
                setProjects(projects.filter(p => p.id !== projectId));
                toast({
                  title: "Success",
                  description: "Project deleted successfully",
                });
              } else {
                throw new Error('Failed to delete project');
              }
            } catch (error) {
              console.error('Error deleting project:', error);
              toast({
                title: "Error",
                description: "Failed to delete project",
                variant: "destructive",
              });
            } finally {
              setDeletingProjectId(null);
            }
          }
        }
      ]
    );
  };

  const handleSendInvite = async () => {
    if (!selectedProject || !inviteEmail.trim()) {
      Alert.alert('Error', 'Please fill in all required fields');
      return;
    }

    try {
      setIsSendingInvite(true);
      const token = await AsyncStorage.getItem('token');
      const response = await fetch(`${API_URL}/admin/projects/${selectedProject.id}/invite`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          email: inviteEmail.trim(),
          role: inviteRole,
        }),
      });

      if (response.ok) {
        toast({
          title: "Success",
          description: "Invitation sent successfully",
        });
        setIsInviteDialogOpen(false);
        setInviteEmail('');
        setInviteRole('contributor');
        setSelectedProject(null);
      } else {
        const error = await response.json();
        throw new Error(error.message || 'Failed to send invitation');
      }
    } catch (error) {
      console.error('Error sending invitation:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to send invitation",
        variant: "destructive",
      });
    } finally {
      setIsSendingInvite(false);
    }
  };

  const handleUpdateTicketStatus = async (ticketId: number, status: string) => {
    try {
      const token = await AsyncStorage.getItem('token');
      const response = await fetch(`${API_URL}/support/admin/tickets/${ticketId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({ status }),
      });

      if (response.ok) {
        // Update local state
        setSupportTickets(tickets =>
          tickets.map(ticket =>
            ticket.id === ticketId ? { ...ticket, status: status as any } : ticket
          )
        );
        toast({
          title: "Success",
          description: "Ticket status updated successfully",
        });
      } else {
        throw new Error('Failed to update ticket status');
      }
    } catch (error) {
      console.error('Error updating ticket status:', error);
      toast({
        title: "Error",
        description: "Failed to update ticket status",
        variant: "destructive",
      });
    }
  };

  const handleSendTicketReply = async () => {
    if (!selectedTicket || !ticketReply.trim()) {
      Alert.alert('Error', 'Please enter a reply message');
      return;
    }

    try {
      setIsSendingReply(true);
      const token = await AsyncStorage.getItem('token');
      const response = await fetch(`${API_URL}/support/admin/tickets/${selectedTicket.id}/message`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          message: ticketReply.trim(),
          isInternal: false,
        }),
      });

      if (response.ok) {
        toast({
          title: "Success",
          description: "Reply sent successfully",
        });
        setTicketReply('');
        setSelectedTicket(null);
        // Refresh tickets
        fetchSupportTickets();
      } else {
        throw new Error('Failed to send reply');
      }
    } catch (error) {
      console.error('Error sending reply:', error);
      toast({
        title: "Error",
        description: "Failed to send reply",
        variant: "destructive",
      });
    } finally {
      setIsSendingReply(false);
    }
  };

  const renderTabContent = () => {
    if (isLoading) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors.primary} />
          <Text style={styles.loadingText}>Loading...</Text>
        </View>
      );
    }

    switch (activeTab) {
      case 'users':
        return (
          <View style={styles.tabContent}>
            <Text style={styles.tabTitle}>User Management</Text>
            <Text style={styles.tabDescription}>
              Manage user roles and permissions
            </Text>

            <View style={styles.usersList}>
              {users.map((user) => (
                <Card key={user.id} style={styles.userCard}>
                  <CardContent>
                    <View style={styles.userInfo}>
                      <Text style={styles.userName}>{user.name}</Text>
                      <Text style={styles.userEmail}>{user.email}</Text>
                      <Text style={styles.userDate}>
                        Joined: {new Date(user.createdAt).toLocaleDateString()}
                      </Text>
                    </View>
                    <View style={styles.userActions}>
                      <Text style={styles.roleLabel}>Role:</Text>
                      <View style={styles.pickerContainer}>
                        <Picker
                          selectedValue={selectedRoles[user.id] || user.role}
                          onValueChange={(value) => {
                            setSelectedRoles(prev => ({
                              ...prev,
                              [user.id]: value
                            }));
                            handleRoleChange(user.id, value);
                          }}
                          style={styles.picker}
                        >
                          <Picker.Item label="Admin" value="admin" />
                          <Picker.Item label="Organizer" value="organizer" />
                          <Picker.Item label="Contributor" value="contributor" />
                        </Picker>
                      </View>
                    </View>
                  </CardContent>
                </Card>
              ))}
            </View>
          </View>
        );

      case 'projects':
        return (
          <View style={styles.tabContent}>
            <Text style={styles.tabTitle}>Project Management</Text>
            <Text style={styles.tabDescription}>
              Manage recipe books and projects
            </Text>

            <View style={styles.projectsList}>
              {projects.map((project) => (
                <Card key={project.id} style={styles.projectCard}>
                  <CardContent>
                    <View style={styles.projectHeader}>
                      <Text style={styles.projectName}>{project.name}</Text>
                      <View style={styles.projectActions}>
                        <TouchableOpacity
                          onPress={() => {
                            setSelectedProject(project);
                            setIsInviteDialogOpen(true);
                          }}
                          style={styles.actionButton}
                        >
                          <Icon name="person-add" size={20} color={Colors.primary} />
                        </TouchableOpacity>
                        <TouchableOpacity
                          onPress={() => handleDeleteProject(project.id)}
                          style={styles.actionButton}
                          disabled={deletingProjectId === project.id}
                        >
                          {deletingProjectId === project.id ? (
                            <ActivityIndicator size="small" color={Colors.destructive} />
                          ) : (
                            <Icon name="delete" size={20} color={Colors.destructive} />
                          )}
                        </TouchableOpacity>
                      </View>
                    </View>
                    <Text style={styles.projectDescription}>{project.description}</Text>
                    <View style={styles.projectMeta}>
                      <Text style={styles.projectMetaText}>
                        Organizer: {project.organizer.name}
                      </Text>
                      <Text style={styles.projectMetaText}>
                        Contributors: {project.contributors.length}
                      </Text>
                      <Text style={styles.projectMetaText}>
                        Recipes: {project.recipes.length}
                      </Text>
                      <Text style={styles.projectMetaText}>
                        Status: {project.status}
                      </Text>
                    </View>
                  </CardContent>
                </Card>
              ))}
            </View>
          </View>
        );

      case 'support':
        return (
          <View style={styles.tabContent}>
            <View style={styles.supportHeader}>
              <Text style={styles.tabTitle}>Support Tickets</Text>
              <View style={styles.ticketFilter}>
                <View style={styles.pickerContainer}>
                  <Picker
                    selectedValue={ticketFilter}
                    onValueChange={setTicketFilter}
                    style={styles.picker}
                  >
                    <Picker.Item label="All Tickets" value="all" />
                    <Picker.Item label="Open" value="open" />
                    <Picker.Item label="In Progress" value="in_progress" />
                    <Picker.Item label="Resolved" value="resolved" />
                    <Picker.Item label="Closed" value="closed" />
                  </Picker>
                </View>
              </View>
            </View>

            <View style={styles.ticketsList}>
              {supportTickets.map((ticket) => (
                <Card key={ticket.id} style={styles.ticketCard}>
                  <CardContent>
                    <View style={styles.ticketHeader}>
                      <Text style={styles.ticketSubject}>{ticket.subject}</Text>
                      <View style={[
                        styles.statusBadge,
                        ticket.status === 'open' ? styles.statusOpen :
                        ticket.status === 'in_progress' ? styles.statusInProgress :
                        ticket.status === 'resolved' ? styles.statusResolved :
                        styles.statusClosed
                      ]}>
                        <Text style={styles.statusText}>{ticket.status.replace('_', ' ')}</Text>
                      </View>
                    </View>
                    <Text style={styles.ticketDescription} numberOfLines={2}>
                      {ticket.description}
                    </Text>
                    <View style={styles.ticketMeta}>
                      <Text style={styles.ticketMetaText}>
                        From: {ticket.userEmail}
                      </Text>
                      <Text style={styles.ticketMetaText}>
                        Priority: {ticket.priority}
                      </Text>
                      <Text style={styles.ticketMetaText}>
                        Created: {new Date(ticket.createdAt).toLocaleDateString()}
                      </Text>
                    </View>
                    <View style={styles.ticketActions}>
                      <Button
                        variant="outline"
                        size="sm"
                        onPress={() => setSelectedTicket(ticket)}
                      >
                        Reply
                      </Button>
                      <View style={styles.statusActions}>
                        {ticket.status !== 'resolved' && (
                          <Button
                            variant="outline"
                            size="sm"
                            onPress={() => handleUpdateTicketStatus(ticket.id, 'resolved')}
                          >
                            Resolve
                          </Button>
                        )}
                        {ticket.status !== 'closed' && (
                          <Button
                            variant="outline"
                            size="sm"
                            onPress={() => handleUpdateTicketStatus(ticket.id, 'closed')}
                          >
                            Close
                          </Button>
                        )}
                      </View>
                    </View>
                  </CardContent>
                </Card>
              ))}
            </View>
          </View>
        );

      case 'intercom':
        return (
          <View style={styles.tabContent}>
            <Text style={styles.tabTitle}>Live Chat Management</Text>
            <Text style={styles.tabDescription}>
              Manage Intercom live chat settings and conversations
            </Text>

            <Card style={styles.intercomCard}>
              <CardContent>
                <Text style={styles.intercomTitle}>Intercom Integration</Text>
                <Text style={styles.intercomDescription}>
                  Configure Intercom for live chat support. This feature requires the Intercom React Native SDK.
                </Text>
                <Button
                  onPress={() => {
                    Alert.alert(
                      'Intercom Setup',
                      'To enable Intercom live chat:\n\n1. Install @intercom/intercom-react-native\n2. Configure your Intercom App ID\n3. Set up user authentication\n4. Enable the chat widget\n\nThis provides real-time customer support capabilities.'
                    );
                  }}
                  style={styles.intercomButton}
                >
                  Setup Instructions
                </Button>
              </CardContent>
            </Card>
          </View>
        );

      default:
        return null;
    }
  };

  return (
    <ScrollView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>Admin Dashboard</Text>
      </View>

      {/* Quick Actions Card */}
      <Card style={styles.card}>
        <CardHeader>
          <Text style={styles.cardTitle}>Quick Actions</Text>
          <Text style={styles.cardDescription}>Common administrative tasks</Text>
        </CardHeader>
        <CardContent>
          <Button
            variant="outline"
            onPress={() => setLocation('/admin/dashboard')}
            style={styles.quickActionButton}
          >
            <Icon name="check-circle" size={16} color={Colors.foreground} />
            <Text style={styles.quickActionText}>Review Recipe Submissions</Text>
          </Button>
        </CardContent>
      </Card>

      {/* Tabs */}
      <View style={styles.tabsContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.tabsScrollView}>
          {[
            { key: 'users', label: 'Users', icon: 'people' },
            { key: 'projects', label: 'Projects', icon: 'folder' },
            { key: 'support', label: 'Support', icon: 'support' },
            { key: 'intercom', label: 'Live Chat', icon: 'chat' },
          ].map((tab) => (
            <TouchableOpacity
              key={tab.key}
              style={[
                styles.tab,
                activeTab === tab.key && styles.activeTab
              ]}
              onPress={() => setActiveTab(tab.key as any)}
            >
              <Icon
                name={tab.icon}
                size={16}
                color={activeTab === tab.key ? Colors.primaryForeground : Colors.foreground}
              />
              <Text style={[
                styles.tabText,
                activeTab === tab.key && styles.activeTabText
              ]}>
                {tab.label}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* Tab Content */}
      <View style={styles.contentContainer}>
        {renderTabContent()}
      </View>

      {/* Invite Dialog */}
      {isInviteDialogOpen && selectedProject && (
        <Modal
          visible={isInviteDialogOpen}
          transparent
          animationType="fade"
          onRequestClose={() => setIsInviteDialogOpen(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.inviteDialog}>
              <Text style={styles.inviteDialogTitle}>
                Invite to {selectedProject.name}
              </Text>

              <View style={styles.inviteForm}>
                <View style={styles.inputGroup}>
                  <Text style={styles.label}>Email Address</Text>
                  <Input
                    value={inviteEmail}
                    onChangeText={setInviteEmail}
                    placeholder="Enter email address"
                    keyboardType="email-address"
                    autoCapitalize="none"
                  />
                </View>

                <View style={styles.inputGroup}>
                  <Text style={styles.label}>Role</Text>
                  <View style={styles.pickerContainer}>
                    <Picker
                      selectedValue={inviteRole}
                      onValueChange={setInviteRole}
                      style={styles.picker}
                    >
                      <Picker.Item label="Contributor" value="contributor" />
                      <Picker.Item label="Organizer" value="organizer" />
                    </Picker>
                  </View>
                </View>
              </View>

              <View style={styles.inviteDialogActions}>
                <Button
                  variant="outline"
                  onPress={() => {
                    setIsInviteDialogOpen(false);
                    setInviteEmail('');
                    setInviteRole('contributor');
                    setSelectedProject(null);
                  }}
                  style={styles.inviteDialogButton}
                >
                  Cancel
                </Button>
                <Button
                  onPress={handleSendInvite}
                  disabled={isSendingInvite}
                  style={styles.inviteDialogButton}
                >
                  {isSendingInvite ? (
                    <>
                      <ActivityIndicator size="small" color={Colors.primaryForeground} />
                      <Text style={styles.inviteButtonText}>Sending...</Text>
                    </>
                  ) : (
                    'Send Invite'
                  )}
                </Button>
              </View>
            </View>
          </View>
        </Modal>
      )}

      {/* Ticket Reply Dialog */}
      {selectedTicket && (
        <Modal
          visible={!!selectedTicket}
          transparent
          animationType="fade"
          onRequestClose={() => setSelectedTicket(null)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.ticketDialog}>
              <Text style={styles.ticketDialogTitle}>
                Reply to: {selectedTicket.subject}
              </Text>

              <View style={styles.ticketInfo}>
                <Text style={styles.ticketInfoText}>
                  From: {selectedTicket.userEmail}
                </Text>
                <Text style={styles.ticketInfoText}>
                  Status: {selectedTicket.status.replace('_', ' ')}
                </Text>
                <Text style={styles.ticketInfoText}>
                  Priority: {selectedTicket.priority}
                </Text>
              </View>

              <View style={styles.ticketDescription}>
                <Text style={styles.ticketDescriptionTitle}>Original Message:</Text>
                <Text style={styles.ticketDescriptionText}>
                  {selectedTicket.description}
                </Text>
              </View>

              <View style={styles.replyForm}>
                <Text style={styles.label}>Your Reply</Text>
                <TextInput
                  style={styles.replyTextArea}
                  value={ticketReply}
                  onChangeText={setTicketReply}
                  placeholder="Type your reply..."
                  multiline
                  numberOfLines={4}
                  textAlignVertical="top"
                />
              </View>

              <View style={styles.ticketDialogActions}>
                <Button
                  variant="outline"
                  onPress={() => {
                    setSelectedTicket(null);
                    setTicketReply('');
                  }}
                  style={styles.ticketDialogButton}
                >
                  Cancel
                </Button>
                <Button
                  onPress={handleSendTicketReply}
                  disabled={isSendingReply}
                  style={styles.ticketDialogButton}
                >
                  {isSendingReply ? (
                    <>
                      <ActivityIndicator size="small" color={Colors.primaryForeground} />
                      <Text style={styles.replyButtonText}>Sending...</Text>
                    </>
                  ) : (
                    'Send Reply'
                  )}
                </Button>
              </View>
            </View>
          </View>
        </Modal>
      )}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  errorCard: {
    margin: Spacing.lg,
    alignItems: 'center',
  },
  errorIcon: {
    marginBottom: Spacing.md,
  },
  errorText: {
    fontSize: 16,
    color: Colors.destructive,
    textAlign: 'center',
    marginBottom: Spacing.lg,
  },
  backButton: {
    marginTop: Spacing.md,
  },
  header: {
    padding: Spacing.lg,
    backgroundColor: Colors.card,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: Colors.foreground,
  },
  card: {
    margin: Spacing.lg,
    marginBottom: Spacing.md,
  },
  cardTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.cardForeground,
  },
  cardDescription: {
    fontSize: 14,
    color: Colors.mutedForeground,
    marginTop: Spacing.xs,
  },
  quickActionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.sm,
  },
  quickActionText: {
    marginLeft: Spacing.xs,
  },
  tabsContainer: {
    backgroundColor: Colors.card,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  tabsScrollView: {
    paddingHorizontal: Spacing.lg,
  },
  tab: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.xs,
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.md,
    marginRight: Spacing.sm,
    borderRadius: BorderRadius.md,
    backgroundColor: Colors.muted,
  },
  activeTab: {
    backgroundColor: Colors.primary,
  },
  tabText: {
    fontSize: 14,
    color: Colors.foreground,
    fontWeight: '500',
  },
  activeTabText: {
    color: Colors.primaryForeground,
    fontWeight: '600',
  },
  contentContainer: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: Spacing.xl,
  },
  loadingText: {
    marginTop: Spacing.md,
    fontSize: 16,
    color: Colors.foreground,
  },
  tabContent: {
    padding: Spacing.lg,
  },
  tabTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.foreground,
    marginBottom: Spacing.sm,
  },
  tabDescription: {
    fontSize: 16,
    color: Colors.mutedForeground,
    marginBottom: Spacing.xl,
  },
  usersList: {
    gap: Spacing.md,
  },
  userCard: {
    marginBottom: Spacing.md,
  },
  userInfo: {
    marginBottom: Spacing.md,
  },
  userName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.foreground,
    marginBottom: Spacing.xs,
  },
  userEmail: {
    fontSize: 14,
    color: Colors.mutedForeground,
    marginBottom: Spacing.xs,
  },
  userDate: {
    fontSize: 12,
    color: Colors.mutedForeground,
  },
  userActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.md,
  },
  roleLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.foreground,
  },
  pickerContainer: {
    flex: 1,
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: BorderRadius.md,
    backgroundColor: Colors.card,
  },
  picker: {
    color: Colors.foreground,
  },
  projectsList: {
    gap: Spacing.md,
  },
  projectCard: {
    marginBottom: Spacing.md,
  },
  projectHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: Spacing.sm,
  },
  projectName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.foreground,
    flex: 1,
    marginRight: Spacing.md,
  },
  projectActions: {
    flexDirection: 'row',
    gap: Spacing.sm,
  },
  actionButton: {
    padding: Spacing.sm,
    borderRadius: BorderRadius.sm,
  },
  projectDescription: {
    fontSize: 14,
    color: Colors.foreground,
    marginBottom: Spacing.md,
    lineHeight: 20,
  },
  projectMeta: {
    gap: Spacing.xs,
  },
  projectMetaText: {
    fontSize: 12,
    color: Colors.mutedForeground,
  },
  supportHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.lg,
  },
  ticketFilter: {
    minWidth: 150,
  },
  ticketsList: {
    gap: Spacing.md,
  },
  ticketCard: {
    marginBottom: Spacing.md,
  },
  ticketHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: Spacing.sm,
  },
  ticketSubject: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.foreground,
    flex: 1,
    marginRight: Spacing.md,
  },
  statusBadge: {
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.sm,
  },
  statusOpen: {
    backgroundColor: '#fee2e2',
  },
  statusInProgress: {
    backgroundColor: '#fef3c7',
  },
  statusResolved: {
    backgroundColor: '#dcfce7',
  },
  statusClosed: {
    backgroundColor: '#f3f4f6',
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
    textTransform: 'capitalize',
    color: Colors.foreground,
  },
  ticketDescription: {
    fontSize: 14,
    color: Colors.foreground,
    marginBottom: Spacing.md,
    lineHeight: 20,
  },
  ticketMeta: {
    gap: Spacing.xs,
    marginBottom: Spacing.md,
  },
  ticketMetaText: {
    fontSize: 12,
    color: Colors.mutedForeground,
  },
  ticketActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  statusActions: {
    flexDirection: 'row',
    gap: Spacing.sm,
  },
  intercomCard: {
    backgroundColor: Colors.muted,
  },
  intercomTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.foreground,
    marginBottom: Spacing.sm,
  },
  intercomDescription: {
    fontSize: 14,
    color: Colors.mutedForeground,
    marginBottom: Spacing.lg,
    lineHeight: 20,
  },
  intercomButton: {
    alignSelf: 'flex-start',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: Spacing.lg,
  },
  inviteDialog: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    padding: Spacing.xl,
    width: '100%',
    maxWidth: 400,
  },
  inviteDialogTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.foreground,
    marginBottom: Spacing.lg,
  },
  inviteForm: {
    marginBottom: Spacing.xl,
  },
  inputGroup: {
    marginBottom: Spacing.lg,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.foreground,
    marginBottom: Spacing.sm,
  },
  inviteDialogActions: {
    flexDirection: 'row',
    gap: Spacing.md,
  },
  inviteDialogButton: {
    flex: 1,
  },
  inviteButtonText: {
    color: Colors.primaryForeground,
    marginLeft: Spacing.xs,
  },
  ticketDialog: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    padding: Spacing.xl,
    width: '100%',
    maxWidth: 500,
    maxHeight: '80%',
  },
  ticketDialogTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.foreground,
    marginBottom: Spacing.lg,
  },
  ticketInfo: {
    backgroundColor: Colors.muted,
    padding: Spacing.md,
    borderRadius: BorderRadius.md,
    marginBottom: Spacing.lg,
  },
  ticketInfoText: {
    fontSize: 14,
    color: Colors.foreground,
    marginBottom: Spacing.xs,
  },
  ticketDescription: {
    marginBottom: Spacing.lg,
  },
  ticketDescriptionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.foreground,
    marginBottom: Spacing.sm,
  },
  ticketDescriptionText: {
    fontSize: 14,
    color: Colors.foreground,
    lineHeight: 20,
    backgroundColor: Colors.muted,
    padding: Spacing.md,
    borderRadius: BorderRadius.md,
  },
  replyForm: {
    marginBottom: Spacing.xl,
  },
  replyTextArea: {
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: BorderRadius.md,
    padding: Spacing.md,
    fontSize: 16,
    backgroundColor: Colors.background,
    color: Colors.foreground,
    minHeight: 100,
    textAlignVertical: 'top',
  },
  ticketDialogActions: {
    flexDirection: 'row',
    gap: Spacing.md,
  },
  ticketDialogButton: {
    flex: 1,
  },
  replyButtonText: {
    color: Colors.primaryForeground,
    marginLeft: Spacing.xs,
  },
});